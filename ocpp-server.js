const WebSocket = require('ws');
const http = require('http');
const url = require('url');

// 建立HTTP伺服器
const server = http.createServer();

// 建立WebSocket伺服器
const wss = new WebSocket.Server({
    server,
    verifyClient: (info) => {
        // 檢查WebSocket子協定
        const protocols = info.req.headers['sec-websocket-protocol'];
        if (protocols && protocols.includes('ocpp1.6')) {
            return true;
        }
        console.log('❌ 連接被拒絕：缺少 ocpp1.6 協定');
        return false;
    }
});

// 儲存已連接的充電樁
const connectedChargePoints = new Map();
let messageId = 1;

// 產生唯一訊息ID
function generateMessageId() {
    return `central_${Date.now()}_${messageId++}`;
}

// 記錄日誌
function log(message, type = 'info') {
    const time = new Date().toLocaleTimeString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'send' ? '📤' : type === 'receive' ? '📥' : 'ℹ️';
    console.log(`[${time}] ${prefix} ${message}`);
}

// 發送OCPP訊息
function sendMessage(ws, messageType, action, payload, originalMessageId = null) {
    const message = originalMessageId 
        ? [messageType, originalMessageId, payload] // CALLRESULT
        : [messageType, generateMessageId(), action, payload]; // CALL
    
    const messageString = JSON.stringify(message);
    log(`發送給充電樁: ${messageString}`, 'send');
    ws.send(messageString);
}

// 處理收到的OCPP訊息
function handleMessage(ws, chargePointId, data) {
    try {
        const message = JSON.parse(data);
        log(`從 ${chargePointId} 收到: ${data}`, 'receive');

        const [messageType, messageId, actionOrStatus, payload] = message;

        switch (messageType) {
            case 2: // CALL - 來自充電樁的請求
                handleChargePointCall(ws, chargePointId, messageId, actionOrStatus, payload);
                break;
            case 3: // CALLRESULT - 充電樁的回應
                log(`${chargePointId} 回應成功: ${JSON.stringify(payload)}`, 'success');
                break;
            case 4: // CALLERROR - 充電樁的錯誤回應
                log(`${chargePointId} 回應錯誤: ${actionOrStatus}`, 'error');
                break;
        }
    } catch (error) {
        log(`解析訊息失敗: ${error.message}`, 'error');
    }
}

// 處理來自充電樁的CALL訊息
function handleChargePointCall(ws, chargePointId, messageId, action, payload) {
    log(`處理 ${chargePointId} 的 ${action} 請求`, 'info');

    switch (action) {
        case 'BootNotification':
            // 充電樁啟動通知
            const bootResponse = {
                currentTime: new Date().toISOString(),
                interval: 300, // 心跳間隔（秒）
                status: 'Accepted'
            };
            sendMessage(ws, 3, null, bootResponse, messageId);
            log(`${chargePointId} 啟動通知已接受`, 'success');
            break;

        case 'Heartbeat':
            // 心跳回應
            const heartbeatResponse = {
                currentTime: new Date().toISOString()
            };
            sendMessage(ws, 3, null, heartbeatResponse, messageId);
            log(`${chargePointId} 心跳回應`, 'success');
            break;

        case 'StatusNotification':
            // 狀態通知回應
            sendMessage(ws, 3, null, {}, messageId);
            log(`${chargePointId} 狀態: ${payload.status}`, 'info');
            break;

        case 'StartTransaction':
            // 啟動交易回應
            const startResponse = {
                transactionId: Math.floor(Math.random() * 10000),
                idTagInfo: {
                    status: 'Accepted'
                }
            };
            sendMessage(ws, 3, null, startResponse, messageId);
            log(`${chargePointId} 交易已啟動`, 'success');
            break;

        case 'StopTransaction':
            // 停止交易回應
            const stopResponse = {
                idTagInfo: {
                    status: 'Accepted'
                }
            };
            sendMessage(ws, 3, null, stopResponse, messageId);
            log(`${chargePointId} 交易已停止`, 'success');
            break;

        case 'MeterValues':
            // 電錶數值回應
            sendMessage(ws, 3, null, {}, messageId);
            log(`${chargePointId} 電錶數值已接收`, 'info');
            break;

        default:
            // 未知動作
            const errorResponse = [4, messageId, 'NotImplemented', `動作 ${action} 尚未實作`, {}];
            ws.send(JSON.stringify(errorResponse));
            log(`未實作的動作: ${action}`, 'error');
    }
}

// WebSocket連接處理
wss.on('connection', (ws, request) => {
    // 從URL路徑提取充電樁ID
    const pathname = url.parse(request.url).pathname;
    const chargePointId = pathname.substring(1); // 移除開頭的 '/'

    if (!chargePointId) {
        log('連接被拒絕：缺少充電樁ID', 'error');
        ws.close();
        return;
    }

    log(`✅ 充電樁 ${chargePointId} 已連接`, 'success');
    log(`   來源IP: ${request.socket.remoteAddress}`, 'info');
    log(`   連接協定: ${ws.protocol}`, 'info');

    // 儲存連接
    connectedChargePoints.set(chargePointId, {
        ws: ws,
        connectedAt: new Date(),
        lastHeartbeat: new Date()
    });

    // 選擇正確的子協定
    if (request.headers['sec-websocket-protocol']) {
        ws.protocol = 'ocpp1.6';
    }

    // 訊息處理
    ws.on('message', (data) => {
        handleMessage(ws, chargePointId, data.toString());
    });

    // 連接關閉處理
    ws.on('close', () => {
        log(`❌ 充電樁 ${chargePointId} 已斷線`, 'error');
        connectedChargePoints.delete(chargePointId);
    });

    // 錯誤處理
    ws.on('error', (error) => {
        log(`充電樁 ${chargePointId} 發生錯誤: ${error.message}`, 'error');
    });

    // 發送歡迎訊息（可選）
    setTimeout(() => {
        if (ws.readyState === WebSocket.OPEN) {
            log(`向 ${chargePointId} 發送歡迎訊息`, 'info');
        }
    }, 1000);
});

// 定期顯示連接狀態
setInterval(() => {
    if (connectedChargePoints.size > 0) {
        log(`\n📊 已連接的充電樁: ${connectedChargePoints.size}`, 'info');
        connectedChargePoints.forEach((info, id) => {
            const duration = Math.floor((Date.now() - info.connectedAt) / 1000);
            log(`   • ${id} (連接 ${duration} 秒)`, 'info');
        });
        log('', 'info');
    }
}, 30000); // 每30秒顯示一次

// 啟動伺服器
const PORT = 8080;
server.listen(PORT, '0.0.0.0', () => {
    log(`🚀 OCPP 伺服器已啟動`, 'success');
    log(`   監聽位址: ws://0.0.0.0:${PORT}`, 'info');
    log(`   本機位址: ws://localhost:${PORT}`, 'info');
    log(`   區域網路: ws://192.168.0.x:${PORT}`, 'info');
    log(``, 'info');
    log(`💡 如何連接充電樁:`, 'info');
    log(`   1. 將充電樁的 Server URL 改為: ws://您的電腦IP:${PORT}`, 'info');
    log(`   2. 充電樁ID保持: 20241024NJ1`, 'info');
    log(`   3. 重啟充電樁讓設定生效`, 'info');
    log(``, 'info');
});

// 優雅關閉
process.on('SIGINT', () => {
    log('\n🛑 正在關閉伺服器...', 'info');
    wss.clients.forEach((ws) => {
        ws.close();
    });
    server.close(() => {
        log('✅ 伺服器已關閉', 'success');
        process.exit(0);
    });
});

// 錯誤處理
process.on('uncaughtException', (error) => {
    log(`未捕捉的異常: ${error.message}`, 'error');
});

process.on('unhandledRejection', (reason, promise) => {
    log(`未處理的Promise拒絕: ${reason}`, 'error');
}); 