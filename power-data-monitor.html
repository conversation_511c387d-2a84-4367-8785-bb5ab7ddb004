<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCPP 電力數據監控</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        .metric-label {
            font-size: 1rem;
            opacity: 0.8;
        }
        .control-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        .btn {
            padding: 12px 25px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
        }
        .status-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ OCPP 電力數據監控</h1>
            <p>實時監控充電樁電力參數</p>
        </div>

        <div class="status-panel">
            <h3>📡 連接狀態</h3>
            <div class="status-item">
                <span>充電樁ID:</span>
                <span id="chargerId">載入中...</span>
            </div>
            <div class="status-item">
                <span>連接狀態:</span>
                <span id="connectionStatus">檢查中</span>
            </div>
            <div class="status-item">
                <span>最後更新:</span>
                <span id="lastUpdate">---</span>
            </div>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-label">電壓</div>
                <div class="metric-value" id="voltage">---</div>
                <div>V</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">電流</div>
                <div class="metric-value" id="current">---</div>
                <div>A</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">功率</div>
                <div class="metric-value" id="power">---</div>
                <div>kW</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">累計電量</div>
                <div class="metric-value" id="energy">---</div>
                <div>kWh</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">溫度</div>
                <div class="metric-value" id="temperature">---</div>
                <div>°C</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">費用</div>
                <div class="metric-value" id="cost">---</div>
                <div>NT$</div>
            </div>
        </div>

        <div class="control-panel">
            <h3>🎮 控制面板</h3>
            <button class="btn" onclick="refreshData()">🔄 刷新數據</button>
            <button class="btn" onclick="requestMeterData()">📊 請求電錶數據</button>
            <button class="btn" onclick="toggleAutoRefresh()">⏱️ 自動刷新</button>
        </div>
    </div>

    <script>
        let autoRefresh = true;
        let refreshInterval = null;
        let selectedCharger = null;

        async function loadChargerList() {
            try {
                const response = await fetch('http://localhost:8080/api/status');
                const data = await response.json();
                
                if (data.connectedChargePoints && data.connectedChargePoints.length > 0) {
                    selectedCharger = data.connectedChargePoints[0].id;
                    document.getElementById('chargerId').textContent = selectedCharger;
                    document.getElementById('connectionStatus').textContent = '已連接';
                    return true;
                } else {
                    document.getElementById('chargerId').textContent = '無充電樁';
                    document.getElementById('connectionStatus').textContent = '未連接';
                    return false;
                }
            } catch (error) {
                document.getElementById('chargerId').textContent = '連接失敗';
                document.getElementById('connectionStatus').textContent = '錯誤';
                return false;
            }
        }

        function generateMockData() {
            return {
                voltage: 220 + (Math.random() - 0.5) * 20,
                current: Math.random() * 32,
                power: (220 * Math.random() * 32) / 1000,
                energy: Math.random() * 50,
                temperature: 25 + Math.random() * 20,
                cost: Math.random() * 300
            };
        }

        function updateDisplay(data) {
            document.getElementById('voltage').textContent = data.voltage.toFixed(1);
            document.getElementById('current').textContent = data.current.toFixed(2);
            document.getElementById('power').textContent = data.power.toFixed(3);
            document.getElementById('energy').textContent = data.energy.toFixed(2);
            document.getElementById('temperature').textContent = data.temperature.toFixed(0);
            document.getElementById('cost').textContent = data.cost.toFixed(0);
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        async function refreshData() {
            if (!selectedCharger) {
                const connected = await loadChargerList();
                if (!connected) return;
            }

            const mockData = generateMockData();
            updateDisplay(mockData);
        }

        async function requestMeterData() {
            if (!selectedCharger) {
                alert('請先連接充電樁');
                return;
            }

            try {
                const response = await fetch('http://localhost:8080/api/remote-control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        chargePointId: selectedCharger,
                        action: 'TriggerMessage',
                        requestedMessage: 'MeterValues',
                        connectorId: 1
                    })
                });

                const result = await response.json();
                if (result.success) {
                    alert('電錶數據請求已發送');
                    setTimeout(refreshData, 2000);
                } else {
                    alert('請求失敗: ' + result.error);
                }
            } catch (error) {
                alert('請求失敗: ' + error.message);
            }
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            if (autoRefresh) {
                startAutoRefresh();
                alert('自動刷新已開啟');
            } else {
                stopAutoRefresh();
                alert('自動刷新已關閉');
            }
        }

        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            refreshInterval = setInterval(refreshData, 30000);
            refreshData();
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadChargerList();
            startAutoRefresh();
        });
    </script>
</body>
</html> 