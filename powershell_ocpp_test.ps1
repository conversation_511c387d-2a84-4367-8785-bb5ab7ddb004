# PowerShell OCPP 充電樁連接測試工具
# 使用 .NET WebSocket 類別進行連接測試

param(
    [string]$ServerUrl = "ws://*************:8080",
    [string]$ChargePointId = "20241024NJ1",
    [string]$Protocol = "ocpp1.6"
)

# 載入必要的 .NET 類別
Add-Type -AssemblyName System.Net.WebSockets
Add-Type -AssemblyName System.Threading.Tasks

Write-Host "⚡ PowerShell OCPP 充電樁連接測試工具" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan
Write-Host "📡 伺服器: $ServerUrl" -ForegroundColor Yellow
Write-Host "🔌 充電樁ID: $ChargePointId" -ForegroundColor Yellow
Write-Host "📋 協定: $Protocol" -ForegroundColor Yellow
Write-Host ""

# 全域變數
$script:messageId = 1
$script:websocket = $null
$script:cancellationToken = [System.Threading.CancellationToken]::None

function Get-NextMessageId {
    $timestamp = [DateTimeOffset]::UtcNow.ToUnixTimeMilliseconds()
    $id = "ps_test_$($timestamp)_$($script:messageId)"
    $script:messageId++
    return $id
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "Info"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "Error" { "Red" }
        "Warning" { "Yellow" }
        "Success" { "Green" }
        default { "White" }
    }
    
    Write-Host "[$timestamp] $Message" -ForegroundColor $color
}

function Send-OCPPMessage {
    param(
        [array]$Message,
        [string]$ActionName
    )
    
    try {
        $jsonMessage = $Message | ConvertTo-Json -Compress
        Write-Log "📤 發送 $ActionName`: $jsonMessage" "Info"
        
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($jsonMessage)
        $buffer = New-Object System.ArraySegment[byte] -ArgumentList @(,$bytes)
        
        $task = $script:websocket.SendAsync($buffer, [System.Net.WebSockets.WebSocketMessageType]::Text, $true, $script:cancellationToken)
        $task.Wait()
        
        Write-Log "✅ $ActionName 發送成功" "Success"
    }
    catch {
        Write-Log "❌ 發送 $ActionName 失敗: $($_.Exception.Message)" "Error"
    }
}

function Send-BootNotification {
    $message = @(
        2,  # CALL
        (Get-NextMessageId),
        "BootNotification",
        @{
            chargePointVendor = "PowerShell_Test"
            chargePointModel = "PS_Test_Model_V1"
            chargePointSerialNumber = "PS_TEST_001"
            firmwareVersion = "1.0.0-powershell"
        }
    )
    
    Send-OCPPMessage -Message $message -ActionName "BootNotification"
}

function Send-StatusNotification {
    $message = @(
        2,  # CALL
        (Get-NextMessageId),
        "StatusNotification",
        @{
            connectorId = 1
            errorCode = "NoError"
            status = "Available"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        }
    )
    
    Send-OCPPMessage -Message $message -ActionName "StatusNotification"
}

function Send-Heartbeat {
    $message = @(
        2,  # CALL
        (Get-NextMessageId),
        "Heartbeat",
        @{}
    )
    
    Send-OCPPMessage -Message $message -ActionName "Heartbeat"
}

function Send-MeterValues {
    $message = @(
        2,  # CALL
        (Get-NextMessageId),
        "MeterValues",
        @{
            connectorId = 1
            meterValue = @(
                @{
                    timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                    sampledValue = @(
                        @{ value = "240.2"; measurand = "Voltage"; unit = "V" },
                        @{ value = "12.8"; measurand = "Current.Import"; unit = "A" },
                        @{ value = "3.07"; measurand = "Power.Active.Import"; unit = "kW" },
                        @{ value = "0.95"; measurand = "Energy.Active.Import.Register"; unit = "kWh" },
                        @{ value = "32"; measurand = "Temperature"; unit = "Celsius" }
                    )
                }
            )
        }
    )
    
    Send-OCPPMessage -Message $message -ActionName "MeterValues"
}

function Receive-Messages {
    Write-Log "👂 開始監聽伺服器訊息..." "Info"
    
    $buffer = New-Object byte[] 4096
    $segment = New-Object System.ArraySegment[byte] -ArgumentList @(,$buffer)
    
    while ($script:websocket.State -eq [System.Net.WebSockets.WebSocketState]::Open) {
        try {
            $task = $script:websocket.ReceiveAsync($segment, $script:cancellationToken)
            $result = $task.Result
            
            if ($result.MessageType -eq [System.Net.WebSockets.WebSocketMessageType]::Text) {
                $receivedBytes = $buffer[0..($result.Count - 1)]
                $message = [System.Text.Encoding]::UTF8.GetString($receivedBytes)
                
                Write-Log "📥 收到訊息: $message" "Info"
                
                try {
                    $data = $message | ConvertFrom-Json
                    $messageType = $data[0]
                    
                    switch ($messageType) {
                        3 {  # CALLRESULT
                            $messageId = $data[1]
                            $payload = $data[2] | ConvertTo-Json -Depth 10
                            Write-Log "✅ 收到回應 (ID: $messageId): $payload" "Success"
                        }
                        4 {  # CALLERROR
                            $messageId = $data[1]
                            $errorCode = $data[2]
                            $errorDescription = $data[3]
                            Write-Log "❌ 收到錯誤 (ID: $messageId): $errorCode - $errorDescription" "Error"
                        }
                        2 {  # CALL (來自伺服器的請求)
                            $messageId = $data[1]
                            $action = $data[2]
                            Write-Log "📥 收到伺服器請求: $action" "Info"
                            
                            # 簡單回應
                            $response = @(3, $messageId, @{ status = "Accepted" })
                            Send-OCPPMessage -Message $response -ActionName "Response to $action"
                        }
                    }
                }
                catch {
                    Write-Log "❌ 解析訊息失敗: $($_.Exception.Message)" "Error"
                }
            }
            elseif ($result.MessageType -eq [System.Net.WebSockets.WebSocketMessageType]::Close) {
                Write-Log "⚠️ 伺服器要求關閉連接" "Warning"
                break
            }
        }
        catch {
            Write-Log "❌ 接收訊息時發生錯誤: $($_.Exception.Message)" "Error"
            break
        }
    }
}

function Test-OCPPConnection {
    try {
        # 建立WebSocket連接
        $uri = "$ServerUrl/$ChargePointId"
        Write-Log "🔍 嘗試連接到: $uri" "Info"
        Write-Log "📋 使用協定: $Protocol" "Info"
        
        $script:websocket = New-Object System.Net.WebSockets.ClientWebSocket
        
        # 設定子協定
        $script:websocket.Options.AddSubProtocol($Protocol)
        
        # 設定超時
        $script:websocket.Options.KeepAliveInterval = [TimeSpan]::FromSeconds(30)
        
        # 連接到伺服器
        $connectTask = $script:websocket.ConnectAsync([Uri]$uri, $script:cancellationToken)
        $connectTask.Wait(10000)  # 10秒超時
        
        if ($script:websocket.State -eq [System.Net.WebSockets.WebSocketState]::Open) {
            Write-Log "✅ 成功連接到OCPP伺服器!" "Success"
            Write-Log "🔗 連接狀態: $($script:websocket.State)" "Info"
            Write-Log "📋 使用的子協定: $($script:websocket.SubProtocol)" "Info"
            
            # 執行OCPP測試序列
            Write-Log "🚀 開始OCPP測試序列" "Info"
            
            # 1. BootNotification
            Send-BootNotification
            Start-Sleep -Seconds 2
            
            # 2. StatusNotification
            Send-StatusNotification
            Start-Sleep -Seconds 2
            
            # 3. Heartbeat
            Send-Heartbeat
            Start-Sleep -Seconds 2
            
            # 4. MeterValues
            Send-MeterValues
            Start-Sleep -Seconds 2
            
            # 開始監聽訊息
            Write-Log "🎧 開始監聽模式，按 Ctrl+C 停止..." "Info"
            Receive-Messages
        }
        else {
            Write-Log "❌ 連接失敗，狀態: $($script:websocket.State)" "Error"
        }
    }
    catch {
        Write-Log "❌ 連接過程中發生錯誤: $($_.Exception.Message)" "Error"
        
        # 分析錯誤
        if ($_.Exception.InnerException) {
            Write-Log "💡 內部錯誤: $($_.Exception.InnerException.Message)" "Warning"
        }
        
        Write-Log "🔧 建議檢查項目:" "Info"
        Write-Log "   • 確認OCPP伺服器正在運行" "Info"
        Write-Log "   • 檢查網路連接" "Info"
        Write-Log "   • 驗證伺服器URL和端口" "Info"
        Write-Log "   • 確認防火牆設定" "Info"
    }
    finally {
        # 清理資源
        if ($script:websocket -and $script:websocket.State -eq [System.Net.WebSockets.WebSocketState]::Open) {
            try {
                $closeTask = $script:websocket.CloseAsync([System.Net.WebSockets.WebSocketCloseStatus]::NormalClosure, "Test completed", $script:cancellationToken)
                $closeTask.Wait(5000)
                Write-Log "🔌 連接已正常關閉" "Info"
            }
            catch {
                Write-Log "⚠️ 關閉連接時發生錯誤: $($_.Exception.Message)" "Warning"
            }
        }
        
        if ($script:websocket) {
            $script:websocket.Dispose()
        }
        
        Write-Log "🏁 測試完成" "Info"
    }
}

# 主程序
try {
    Test-OCPPConnection
}
catch {
    Write-Log "❌ 程序執行失敗: $($_.Exception.Message)" "Error"
}

Write-Host ""
Write-Host "按任意鍵退出..." -ForegroundColor Gray