{"name": "ocpp-web-client", "version": "1.0.0", "description": "OCPP 1.6 Web Client with Remote Control and Monitoring", "main": "app.js", "scripts": {"start": "npx live-server --port=3000 --open=/", "server": "node ocpp-server.js", "enhanced-server": "node enhanced-ocpp-server.js", "test": "node test-ocpp.js", "remote-test": "node remote-control-test.js", "monitor": "start http://localhost:3000/enhanced-dashboard.html", "dev": "concurrently \"npm run enhanced-server\" \"npm run start\"", "help": "echo Available commands: start, server, enhanced-server, test, remote-test, monitor, dev"}, "keywords": ["OCPP", "WebSocket", "EV", "charging", "remote-control", "monitoring"], "author": "Your Name", "license": "MIT", "devDependencies": {"live-server": "^1.2.2", "http-server": "^14.1.1", "concurrently": "^8.2.2"}, "dependencies": {"ws": "^8.14.2"}, "repository": {"type": "git", "url": "https://github.com/afaxpower/ocpp-web-client"}, "engines": {"node": ">=14.0.0"}}