# OCPP 網頁客戶端

一個簡單易用的 OCPP 1.6 網頁客戶端，可以模擬充電樁與 OCPP 伺服器的通訊。

## 功能特色

- ✅ **WebSocket 連線**：支援 OCPP 1.6 over WebSocket 協定
- ✅ **即時通訊**：即時發送和接收 OCPP 訊息
- ✅ **基本 OCPP 訊息**：支援 BootNotification、Heartbeat、StatusNotification
- ✅ **伺服器請求處理**：自動處理來自伺服器的 Reset、RemoteStartTransaction 等請求
- ✅ **美觀介面**：現代化響應式設計
- ✅ **詳細日誌**：完整的訊息發送和接收記錄

## 快速開始

### 方法一：直接開啟網頁

1. 下載或複製專案檔案
2. 直接用瀏覽器開啟 `index.html` 檔案

### 方法二：使用本地伺服器（推薦）

1. 確保已安裝 Node.js (版本 14 或以上)

2. 安裝專案依賴：
   ```bash
   npm install
   ```

3. 啟動開發伺服器：
   ```bash
   npm start
   ```

4. 瀏覽器會自動開啟 `http://localhost:8080`

### 方法三：使用 VS Code Live Server

1. 在 VS Code 中安裝 "Live Server" 擴充功能
2. 右鍵點擊 `index.html` 檔案
3. 選擇 "Open with Live Server"

## 使用說明

### 基本連線

1. **輸入伺服器資訊**：
   - Server URL：OCPP 伺服器的 WebSocket URL
   - Charge Point ID：充電樁的唯一識別碼

2. **建立連線**：
   - 點擊「連線 (Connect)」按鈕
   - 觀察連線狀態和日誌訊息

3. **發送訊息**：
   - 連線成功後，可使用控制面板的按鈕發送各種 OCPP 訊息
   - 系統會自動發送 BootNotification 和定期心跳

### 支援的 OCPP 訊息

#### 充電樁發送的訊息（Charge Point → Central System）

- **BootNotification**：充電樁啟動通知
- **Heartbeat**：心跳訊息（每30秒自動發送）
- **StatusNotification**：充電樁狀態通知

#### 伺服器請求處理（Central System → Charge Point）

- **Reset**：重置充電樁
- **RemoteStartTransaction**：遠端啟動充電
- **RemoteStopTransaction**：遠端停止充電

## 檔案結構

```
OCPP_Web_Client/
├── index.html          # 主要網頁文件
├── style.css           # 樣式表
├── app.js              # JavaScript 邏輯
├── package.json        # Node.js 專案設定
└── README.md           # 專案說明文件
```

## 技術規格

- **前端技術**：HTML5, CSS3, JavaScript (ES6+)
- **通訊協定**：WebSocket with OCPP 1.6J (JSON)
- **瀏覽器支援**：Chrome, Firefox, Safari, Edge (現代瀏覽器)
- **相依性**：無外部函式庫依賴（純原生 JavaScript）

## OCPP 訊息格式

本客戶端遵循 OCPP 1.6J 規範，使用以下訊息格式：

### CALL 訊息（請求）
```json
[2, "unique-message-id", "Action", {"key": "value"}]
```

### CALLRESULT 訊息（成功回應）
```json
[3, "unique-message-id", {"key": "value"}]
```

### CALLERROR 訊息（錯誤回應）
```json
[4, "unique-message-id", "ErrorCode", "ErrorDescription", {"key": "value"}]
```

## 開發除錯

### 常見問題

1. **連線失敗**：
   - 檢查伺服器 URL 是否正確
   - 確認網路連線
   - 檢查伺服器是否支援 OCPP 1.6J over WebSocket

2. **訊息發送失敗**：
   - 確認 WebSocket 連線狀態
   - 檢查瀏覽器開發者工具的 Console 面板

3. **CORS 錯誤**：
   - 使用本地伺服器而非直接開啟檔案
   - 確認伺服器設定允許跨域請求

### 瀏覽器除錯

按 F12 開啟開發者工具：
- **Console**：查看 JavaScript 錯誤和日誌
- **Network**：監控 WebSocket 連線狀態
- **Application > WebSockets**：查看即時 WebSocket 通訊

## 進階功能

### 自訂充電樁資訊

修改 `app.js` 中的 `sendBootNotification()` 函式，更改充電樁的廠商和型號資訊：

```javascript
const payload = {
    chargePointVendor: "您的廠商名稱",
    chargePointModel: "您的充電樁型號",
    // ... 其他參數
};
```

### 新增更多 OCPP 訊息

在 `app.js` 中新增按鈕事件處理器和對應的訊息發送函式。

## 授權條款

本專案採用 MIT 授權條款。詳見 [LICENSE](LICENSE) 檔案。

## 貢獻指南

歡迎提交 Issue 和 Pull Request 來改善這個專案。

## 聯絡資訊

- 作者：AFAX POWER
- 專案：OCPP Web Client
- 版本：1.0.0

---

**注意**：本工具僅供開發和測試使用，請勿用於生產環境的充電樁通訊。 