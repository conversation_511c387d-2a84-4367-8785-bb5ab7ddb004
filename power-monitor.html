<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCPP 電力數據監控</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .header h1 {
            font-size: 2.5rem;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .metric-icon {
            font-size: 3rem;
            margin-bottom: 10px;
            display: block;
        }
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 10px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        .metric-label {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        .metric-unit {
            font-size: 1rem;
            opacity: 0.7;
        }
        .chart-container {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .chart-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            text-align: center;
        }
        .chart {
            width: 100%;
            height: 200px;
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        .chart-line {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to top, transparent, rgba(0,255,100,0.3));
        }
        .status-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .status-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        .status-card h3 {
            margin: 0 0 15px 0;
            font-size: 1.3rem;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-label {
            font-weight: bold;
        }
        .status-value {
            color: #00ff88;
        }
        .warning {
            color: #ffaa00 !important;
        }
        .danger {
            color: #ff4444 !important;
        }
        .control-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        .btn {
            padding: 12px 25px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .btn-primary { background: linear-gradient(135deg, #007bff, #0056b3); }
        .btn-success { background: linear-gradient(135deg, #28a745, #1e7e34); }
        .btn-warning { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .auto-refresh {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ OCPP 電力數據監控中心</h1>
            <p>實時監控充電樁電力參數</p>
        </div>

        <!-- 即時電力數據 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <span class="metric-icon">⚡</span>
                <div class="metric-label">電壓</div>
                <div class="metric-value" id="voltage">---</div>
                <div class="metric-unit">V</div>
            </div>
            <div class="metric-card">
                <span class="metric-icon">🔋</span>
                <div class="metric-label">電流</div>
                <div class="metric-value" id="current">---</div>
                <div class="metric-unit">A</div>
            </div>
            <div class="metric-card">
                <span class="metric-icon">💡</span>
                <div class="metric-label">功率</div>
                <div class="metric-value" id="power">---</div>
                <div class="metric-unit">kW</div>
            </div>
            <div class="metric-card">
                <span class="metric-icon">📊</span>
                <div class="metric-label">累計電量</div>
                <div class="metric-value" id="energy">---</div>
                <div class="metric-unit">kWh</div>
            </div>
            <div class="metric-card">
                <span class="metric-icon">🌡️</span>
                <div class="metric-label">溫度</div>
                <div class="metric-value" id="temperature">---</div>
                <div class="metric-unit">°C</div>
            </div>
            <div class="metric-card">
                <span class="metric-icon">💰</span>
                <div class="metric-label">費用</div>
                <div class="metric-value" id="cost">---</div>
                <div class="metric-unit">NT$</div>
            </div>
        </div>

        <!-- 狀態面板 -->
        <div class="status-panel">
            <div class="status-card">
                <h3>📡 連接狀態</h3>
                <div class="status-item">
                    <span class="status-label">充電樁ID:</span>
                    <span class="status-value" id="chargerId">未連接</span>
                </div>
                <div class="status-item">
                    <span class="status-label">連接狀態:</span>
                    <span class="status-value" id="connectionStatus">離線</span>
                </div>
                <div class="status-item">
                    <span class="status-label">最後更新:</span>
                    <span class="status-value" id="lastUpdate">---</span>
                </div>
                <div class="status-item">
                    <span class="status-label">數據更新率:</span>
                    <span class="status-value" id="updateRate">30秒</span>
                </div>
            </div>

            <div class="status-card">
                <h3>⚙️ 系統狀態</h3>
                <div class="status-item">
                    <span class="status-label">充電狀態:</span>
                    <span class="status-value" id="chargingStatus">待機</span>
                </div>
                <div class="status-item">
                    <span class="status-label">連接器狀態:</span>
                    <span class="status-value" id="connectorStatus">可用</span>
                </div>
                <div class="status-item">
                    <span class="status-label">交易ID:</span>
                    <span class="status-value" id="transactionId">---</span>
                </div>
                <div class="status-item">
                    <span class="status-label">充電時間:</span>
                    <span class="status-value" id="chargingTime">00:00:00</span>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>🎮 監控控制</h3>
            <button class="btn btn-primary" onclick="refreshData()">🔄 立即刷新</button>
            <button class="btn btn-success" onclick="requestMeterData()">📊 請求電錶數據</button>
            <button class="btn btn-warning" onclick="exportData()">📁 匯出數據</button>
            
            <div class="auto-refresh">
                <span>自動刷新:</span>
                <label class="switch">
                    <input type="checkbox" id="autoRefresh" checked>
                    <span class="slider"></span>
                </label>
            </div>
        </div>
    </div>

    <script>
        class PowerMonitor {
            constructor() {
                this.serverUrl = 'http://localhost:8080';
                this.selectedCharger = null;
                this.autoRefresh = true;
                this.refreshInterval = null;
                this.dataHistory = [];
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.startAutoRefresh();
                this.loadChargerList();
                this.log('電力監控系統初始化完成');
            }

            setupEventListeners() {
                document.getElementById('autoRefresh').addEventListener('change', (e) => {
                    this.autoRefresh = e.target.checked;
                    if (this.autoRefresh) {
                        this.startAutoRefresh();
                    } else {
                        this.stopAutoRefresh();
                    }
                });
            }

            startAutoRefresh() {
                if (this.refreshInterval) clearInterval(this.refreshInterval);
                this.refreshInterval = setInterval(() => {
                    this.refreshData();
                }, 30000); // 每30秒刷新一次
                this.refreshData(); // 立即刷新一次
            }

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
            }

            async loadChargerList() {
                try {
                    const response = await fetch(`${this.serverUrl}/api/status`);
                    const data = await response.json();
                    
                    if (data.connectedChargePoints && data.connectedChargePoints.length > 0) {
                        this.selectedCharger = data.connectedChargePoints[0].id;
                        this.updateConnectionStatus(true, this.selectedCharger);
                        this.refreshData();
                    } else {
                        this.updateConnectionStatus(false, '無充電樁連接');
                    }
                } catch (error) {
                    console.error('載入充電樁列表失敗:', error);
                    this.updateConnectionStatus(false, '連接失敗');
                }
            }

            updateConnectionStatus(connected, chargerId) {
                document.getElementById('chargerId').textContent = chargerId;
                document.getElementById('connectionStatus').textContent = connected ? '在線' : '離線';
                document.getElementById('connectionStatus').className = connected ? 'status-value' : 'status-value danger';
            }

            async refreshData() {
                if (!this.selectedCharger) {
                    this.loadChargerList();
                    return;
                }

                try {
                    // 模擬從充電樁獲取電力數據
                    const mockData = this.generateMockData();
                    this.updateDisplay(mockData);
                    this.dataHistory.push({
                        timestamp: new Date(),
                        ...mockData
                    });

                    // 保留最近100筆數據
                    if (this.dataHistory.length > 100) {
                        this.dataHistory.shift();
                    }

                    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
                } catch (error) {
                    console.error('刷新數據失敗:', error);
                }
            }

            generateMockData() {
                // 模擬真實的充電樁電力數據
                const baseVoltage = 220;
                const voltage = baseVoltage + (Math.random() - 0.5) * 20; // 210-230V
                const current = Math.random() * 32; // 0-32A
                const power = (voltage * current) / 1000; // kW
                const energy = Math.random() * 50; // 0-50 kWh
                const temperature = 25 + Math.random() * 20; // 25-45°C
                const cost = energy * 6.5; // 假設每度電6.5元

                return {
                    voltage: voltage,
                    current: current,
                    power: power,
                    energy: energy,
                    temperature: temperature,
                    cost: cost
                };
            }

            updateDisplay(data) {
                // 更新數值顯示
                document.getElementById('voltage').textContent = data.voltage.toFixed(1);
                document.getElementById('current').textContent = data.current.toFixed(2);
                document.getElementById('power').textContent = data.power.toFixed(3);
                document.getElementById('energy').textContent = data.energy.toFixed(2);
                document.getElementById('temperature').textContent = data.temperature.toFixed(0);
                document.getElementById('cost').textContent = data.cost.toFixed(0);

                // 根據數值設定顏色警告
                this.setValueColor('voltage', data.voltage, 200, 240);
                this.setValueColor('current', data.current, 30, 35);
                this.setValueColor('temperature', data.temperature, 40, 50);

                // 更新充電狀態
                if (data.current > 1) {
                    document.getElementById('chargingStatus').textContent = '充電中';
                    document.getElementById('chargingStatus').className = 'status-value';
                } else {
                    document.getElementById('chargingStatus').textContent = '待機';
                    document.getElementById('chargingStatus').className = 'status-value warning';
                }
            }

            setValueColor(elementId, value, warningThreshold, dangerThreshold) {
                const element = document.getElementById(elementId);
                if (value > dangerThreshold) {
                    element.className = 'metric-value danger';
                } else if (value > warningThreshold) {
                    element.className = 'metric-value warning';
                } else {
                    element.className = 'metric-value';
                }
            }

            async requestMeterData() {
                if (!this.selectedCharger) {
                    alert('請先連接充電樁');
                    return;
                }

                try {
                    const response = await fetch(`${this.serverUrl}/api/remote-control`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            chargePointId: this.selectedCharger,
                            action: 'TriggerMessage',
                            requestedMessage: 'MeterValues',
                            connectorId: 1
                        })
                    });

                    const result = await response.json();
                    if (result.success) {
                        alert('電錶數據請求已發送');
                        setTimeout(() => this.refreshData(), 2000);
                    } else {
                        alert('請求失敗: ' + result.error);
                    }
                } catch (error) {
                    alert('請求失敗: ' + error.message);
                }
            }

            exportData() {
                if (this.dataHistory.length === 0) {
                    alert('無數據可匯出');
                    return;
                }

                const csvContent = this.generateCSV();
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `power_data_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            generateCSV() {
                const headers = ['時間', '電壓(V)', '電流(A)', '功率(kW)', '電量(kWh)', '溫度(°C)', '費用(NT$)'];
                const rows = this.dataHistory.map(data => [
                    data.timestamp.toLocaleString(),
                    data.voltage.toFixed(1),
                    data.current.toFixed(2),
                    data.power.toFixed(3),
                    data.energy.toFixed(2),
                    data.temperature.toFixed(0),
                    data.cost.toFixed(0)
                ]);

                return [headers, ...rows].map(row => row.join(',')).join('\n');
            }

            log(message) {
                console.log(`[電力監控] ${message}`);
            }
        }

        // 全域函數
        let monitor;

        function refreshData() {
            if (monitor) monitor.refreshData();
        }

        function requestMeterData() {
            if (monitor) monitor.requestMeterData();
        }

        function exportData() {
            if (monitor) monitor.exportData();
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            monitor = new PowerMonitor();
        });
    </script>
</body>
</html> 