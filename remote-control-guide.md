# 遠端控制測試與電力數據監控指南

## 🚀 系統架構

### 已建立的組件
1. **enhanced-ocpp-server.js** - 增強版OCPP伺服器，支援遠端控制
2. **enhanced-dashboard.html** - 美觀的監控界面，顯示電力數據
3. **remote-control-test.js** - 遠端控制測試腳本
4. **真實充電樁** - 20241024NJ1 已連接

## 📊 當前狀態

根據系統日誌顯示：
- ✅ OCPP伺服器正在8080端口運行
- ✅ 充電樁 20241024NJ1 已成功連接
- ✅ 心跳和狀態通知正常
- ✅ 網頁界面在3000端口運行

## 🎮 遠端控制測試步驟

### 方法一：直接使用真實充電樁進行測試

1. **開啟OCPP伺服器終端**
   ```bash
   # 在正在運行的OCPP伺服器終端中輸入命令
   start    # 遠端啟動充電
   stop     # 遠端停止充電  
   reset    # 重啟充電樁
   config   # 讀取設定
   help     # 顯示所有命令
   ```

2. **觀察充電樁反應**
   - 查看充電樁螢幕狀態變化
   - 觀察伺服器日誌回應
   - 檢查是否收到確認訊息

### 方法二：使用測試腳本模擬充電樁

1. **啟動測試腳本**
   ```bash
   node remote-control-test.js
   ```

2. **在OCPP伺服器終端測試命令**
   ```bash
   start    # 測試遠端啟動
   stop     # 測試遠端停止
   reset    # 測試重啟
   config   # 測試讀取設定
   ```

## ⚡ 電力數據監控

### 支援的電力參數
- **電壓 (Voltage)** - V
- **電流 (Current)** - A  
- **功率 (Power)** - kW
- **電量 (Energy)** - kWh
- **溫度 (Temperature)** - °C
- **費用計算** - 元

### 數據來源
1. **MeterValues訊息** - 從充電樁接收實時電錶數據
2. **StatusNotification** - 充電狀態變化
3. **TransactionData** - 交易相關數據

### 查看電力數據的方法

#### 1. 網頁監控界面
- 訪問: `http://localhost:3000/enhanced-dashboard.html`
- 實時顯示電壓、電流、功率等數據
- 包含圖表和趨勢分析

#### 2. OCPP伺服器日誌
```bash
# 觀察日誌中的電錶數據
[17:33:25] ℹ️ 20241024NJ1 電錶數據: Voltage = 220.5 V
[17:33:25] ℹ️ 20241024NJ1 電錶數據: Current.Import = 16.8 A
[17:33:25] ℹ️ 20241024NJ1 電錶數據: Power.Active.Import = 3.7 kW
```

#### 3. 請求電錶數據
在OCPP伺服器終端輸入：
```bash
meter    # 主動請求電錶數據
```

## 🔧 測試項目清單

### 基本功能測試
- [ ] 充電樁連接狀態
- [ ] 心跳訊息交換
- [ ] 狀態通知功能

### 遠端控制測試
- [ ] 遠端啟動充電 (RemoteStartTransaction)
- [ ] 遠端停止充電 (RemoteStopTransaction)  
- [ ] 設備重啟 (Reset)
- [ ] 讀取設定 (GetConfiguration)
- [ ] 觸發訊息 (TriggerMessage)

### 電力數據測試
- [ ] 電壓測量準確性
- [ ] 電流測量準確性
- [ ] 功率計算正確性
- [ ] 電量累計功能
- [ ] 溫度監控
- [ ] 數據更新頻率

## 📈 實時數據監控

### 自動數據收集
系統會自動：
- 每30秒收集一次電錶數據（充電時）
- 每5分鐘顯示連接狀態摘要
- 實時處理所有OCPP訊息

### 手動數據請求
可以透過以下方式主動獲取數據：
1. 在伺服器終端使用 `meter` 命令
2. 在網頁界面點擊「請求數據」按鈕
3. 通過API呼叫觸發數據收集

## 🛠️ 疑難排解

### 常見問題

#### 1. 遠端控制無反應
**檢查項目：**
- 充電樁是否正確連接
- 網路連接是否穩定
- OCPP訊息格式是否正確

**解決方法：**
```bash
# 檢查連接狀態
netstat -an | findstr :8080

# 重啟OCPP伺服器
Ctrl+C
npm run server
```

#### 2. 電力數據不準確
**可能原因：**
- 充電樁電錶校準問題
- 數據傳輸錯誤
- 單位轉換問題

**驗證方法：**
- 對比充電樁顯示數據
- 檢查OCPP訊息格式
- 驗證計算公式

#### 3. 監控界面無數據
**檢查項目：**
- WebSocket連接狀態
- 瀏覽器控制台錯誤
- 伺服器端數據處理

## 📋 測試報告模板

### 遠端控制測試結果
| 功能 | 狀態 | 延遲時間 | 備註 |
|------|------|----------|------|
| 遠端啟動 | ✅/❌ | _秒 | |
| 遠端停止 | ✅/❌ | _秒 | |
| 設備重啟 | ✅/❌ | _秒 | |
| 讀取設定 | ✅/❌ | _秒 | |

### 電力數據測試結果
| 參數 | 實測值 | 充電樁顯示 | 誤差 | 狀態 |
|------|--------|------------|------|------|
| 電壓 | _V | _V | _% | ✅/❌ |
| 電流 | _A | _A | _% | ✅/❌ |
| 功率 | _kW | _kW | _% | ✅/❌ |
| 電量 | _kWh | _kWh | _% | ✅/❌ |

## 🎯 下一步改進建議

### 功能擴展
1. **圖表功能** - 添加實時數據趨勢圖
2. **警報系統** - 異常數據自動警報
3. **數據儲存** - 歷史數據記錄和分析
4. **多充電樁支援** - 同時監控多個設備

### 介面優化
1. **響應式設計** - 支援手機和平板
2. **暗色主題** - 夜間模式
3. **自定義儀表板** - 可配置的監控面板
4. **數據匯出** - CSV/Excel格式匯出

### 性能提升
1. **數據壓縮** - 減少網路傳輸
2. **快取機制** - 提高響應速度
3. **負載平衡** - 支援高併發
4. **容錯處理** - 增強系統穩定性

---

## 📞 技術支援

如遇到問題，請檢查：
1. 系統日誌訊息
2. 網路連接狀態
3. 充電樁設定參數
4. OCPP協定版本相容性

測試完成後，請記錄所有結果並提供改進建議。 