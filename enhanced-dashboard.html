<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCPP 充電樁監控與控制中心</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            padding: 2rem;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e0e0e0;
            margin-bottom: 1.5rem;
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .charger-info {
            display: grid;
            gap: 1rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
        }

        .info-value {
            font-weight: bold;
            color: #2c3e50;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }

        .btn {
            padding: 0.8rem 1.2rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .data-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .metric-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
        }

        .metric-icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: #7f8c8d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-unit {
            font-size: 1.2rem;
            color: #95a5a6;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            border: 2px dashed #bdc3c7;
        }

        .log-container {
            grid-column: 1 / -1;
            background: #2c3e50;
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .log-content {
            background: #34495e;
            border-radius: 8px;
            padding: 1rem;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.3rem 0.5rem;
            border-radius: 4px;
        }

        .log-info { background: rgba(52, 152, 219, 0.2); }
        .log-success { background: rgba(39, 174, 96, 0.2); }
        .log-warning { background: rgba(243, 156, 18, 0.2); }
        .log-error { background: rgba(231, 76, 60, 0.2); }

        .transaction-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }

        .transaction-card {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 1rem;
        }

        .transaction-active {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-color: #ffeaa7;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .control-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>⚡ OCPP 充電樁監控中心</h1>
            <p>實時監控充電樁狀態、電壓數據和充電過程</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <!-- 充電樁狀態卡片 -->
                <div class="card">
                    <h3>
                        <div class="status-indicator" id="statusIndicator"></div>
                        充電樁狀態
                    </h3>
                    <div class="charger-info">
                        <div class="info-item">
                            <span class="info-label">充電樁 ID</span>
                            <span class="info-value" id="chargerId">20241024NJ1</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">連接狀態</span>
                            <span class="info-value" id="connectionStatus">已連接</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">充電狀態</span>
                            <span class="info-value" id="chargingStatus">可用</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">連接時間</span>
                            <span class="info-value" id="connectionTime">00:05:23</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">最後心跳</span>
                            <span class="info-value" id="lastHeartbeat">2秒前</span>
                        </div>
                    </div>
                </div>

                <!-- 遠端控制卡片 -->
                <div class="card">
                    <h3>🎮 遠端控制</h3>
                    <div class="control-buttons">
                        <button class="btn btn-success" onclick="startCharging()">
                            ▶️ 開始充電
                        </button>
                        <button class="btn btn-warning" onclick="stopCharging()">
                            ⏹️ 停止充電
                        </button>
                        <button class="btn btn-primary" onclick="resetCharger()">
                            🔄 重啟設備
                        </button>
                        <button class="btn btn-primary" onclick="getConfiguration()">
                            ⚙️ 讀取設定
                        </button>
                        <button class="btn btn-info" onclick="requestMeterData()">
                            📊 請求電錶數據
                        </button>
                    </div>
                    
                    <div class="transaction-section">
                        <div class="transaction-card" id="transactionCard">
                            <strong>當前交易</strong><br>
                            <span id="transactionStatus">無進行中交易</span>
                        </div>
                        <div class="transaction-card">
                            <strong>交易數量</strong><br>
                            <span id="transactionCount">今日: 0 筆</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="data-section">
                <!-- 電力數據監控 -->
                <div class="card">
                    <h3>⚡ 實時電力數據</h3>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-icon">🔌</div>
                            <div class="metric-value" id="voltage">
                                <span id="voltageValue">220</span>
                                <span class="metric-unit">V</span>
                            </div>
                            <div class="metric-label">電壓</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-icon">⚡</div>
                            <div class="metric-value" id="current">
                                <span id="currentValue">0.0</span>
                                <span class="metric-unit">A</span>
                            </div>
                            <div class="metric-label">電流</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-icon">📊</div>
                            <div class="metric-value" id="power">
                                <span id="powerValue">0.0</span>
                                <span class="metric-unit">kW</span>
                            </div>
                            <div class="metric-label">功率</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-icon">🔋</div>
                            <div class="metric-value" id="energy">
                                <span id="energyValue">0.00</span>
                                <span class="metric-unit">kWh</span>
                            </div>
                            <div class="metric-label">累計電量</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-icon">🌡️</div>
                            <div class="metric-value" id="temperature">
                                <span id="temperatureValue">25</span>
                                <span class="metric-unit">°C</span>
                            </div>
                            <div class="metric-label">溫度</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-icon">💰</div>
                            <div class="metric-value" id="cost">
                                <span id="costValue">0.00</span>
                                <span class="metric-unit">元</span>
                            </div>
                            <div class="metric-label">費用</div>
                        </div>
                    </div>
                </div>

                <!-- 圖表區域 -->
                <div class="card">
                    <h3>📈 數據趨勢圖</h3>
                    <div class="chart-container">
                        <div>
                            <p>📊 即時數據圖表</p>
                            <p style="margin-top: 1rem; color: #bdc3c7;">電壓、電流、功率變化趨勢</p>
                            <p style="margin-top: 0.5rem; font-size: 0.9rem;">圖表功能開發中...</p>
                        </div>
                    </div>
                </div>

                <!-- 系統日誌 -->
                <div class="log-container">
                    <div class="log-header">
                        <h3>📜 系統日誌</h3>
                        <div>
                            <button class="btn btn-primary" onclick="clearLogs()" style="padding: 0.5rem 1rem; font-size: 0.8rem;">
                                清除日誌
                            </button>
                        </div>
                    </div>
                    <div class="log-content" id="logContent">
                        <div class="log-entry log-success">[17:33:20] ✅ 充電樁 20241024NJ1 已連接</div>
                        <div class="log-entry log-info">[17:33:21] ℹ️ BootNotification 已接收</div>
                        <div class="log-entry log-success">[17:33:23] ✅ Heartbeat 正常</div>
                        <div class="log-entry log-info">[17:33:25] ℹ️ StatusNotification: Available</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isCharging = false;
        let currentTransaction = null;
        let chargingInterval = null;
        let startTime = Date.now();
        let serverStatus = 'checking';

        // 實際電力數據 (來自真實充電樁)
        let realData = {
            voltage: 220,    // 從真實充電樁接收
            current: 0,      // 從真實充電樁接收
            power: 0,        // 從真實充電樁接收
            energy: 0,       // 從真實充電樁接收
            temperature: 25, // 從真實充電樁接收
            cost: 0          // 計算得出
        };

        // 檢查OCPP伺服器狀態
        async function checkServerStatus() {
            addLog('🔍 檢查OCPP伺服器連接狀態...', 'info');
            
            try {
                // 嘗試從API獲取狀態
                await fetchChargePointStatus();
                serverStatus = 'connected';
                addLog('✅ OCPP伺服器連接正常', 'success');
                addLog('💡 現在可以使用網頁按鈕進行遠端控制', 'info');
                
                // 定期檢查真實數據更新
                startRealDataMonitoring();
            } catch (error) {
                serverStatus = 'disconnected';
                updateConnectionStatus(false);
                addLog('❌ 無法連接到OCPP伺服器', 'error');
                addLog('💡 請確認OCPP伺服器正在運行', 'info');
                
                // 5秒後重試
                setTimeout(checkServerStatus, 5000);
            }
        }

        // 開始真實數據監控
        function startRealDataMonitoring() {
            // 每15秒從API獲取最新狀態
            setInterval(async () => {
                try {
                    await fetchChargePointStatus();
                } catch (error) {
                    console.log('定期狀態更新失敗:', error.message);
                }
            }, 15000);
            
            // 每30秒自動請求電錶數據
            setInterval(async () => {
                if (serverStatus === 'connected') {
                    try {
                        await requestMeterData();
                    } catch (error) {
                        console.log('自動請求電錶數據失敗:', error.message);
                    }
                }
            }, 30000);
        }

        // 遠端控制功能 (通過HTTP API與OCPP伺服器通信)
        async function startCharging() {
            addLog('🚀 發送遠端啟動充電命令...', 'warning');
            
            try {
                const response = await fetch('http://localhost:8080/api/remote-control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'start',
                        params: {
                            connectorId: 1,
                            idTag: 'WEB_REMOTE_START'
                        }
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`✅ ${result.message}`, 'success');
                    addLog(`📡 訊息ID: ${result.messageId}`, 'info');
                    
                    // 模擬啟動交易
                    currentTransaction = {
                        id: Math.floor(Math.random() * 10000),
                        startTime: new Date(),
                        idTag: 'WEB_REMOTE_START'
                    };
                    updateTransactionStatus();
                } else {
                    addLog(`❌ 啟動充電失敗: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 網路錯誤: ${error.message}`, 'error');
                addLog('💡 請確認OCPP伺服器正在運行', 'info');
            }
        }

        async function stopCharging() {
            addLog('🛑 發送遠端停止充電命令...', 'warning');
            
            try {
                const response = await fetch('http://localhost:8080/api/remote-control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'stop',
                        params: {
                            transactionId: currentTransaction?.id || 1
                        }
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`✅ ${result.message}`, 'success');
                    addLog(`📡 訊息ID: ${result.messageId}`, 'info');
                    
                    // 清除交易狀態
                    if (currentTransaction) {
                        addLog(`🔚 交易 ${currentTransaction.id} 已結束`, 'info');
                    }
                    currentTransaction = null;
                    updateTransactionStatus();
                } else {
                    addLog(`❌ 停止充電失敗: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 網路錯誤: ${error.message}`, 'error');
                addLog('💡 請確認OCPP伺服器正在運行', 'info');
            }
        }

        async function resetCharger() {
            addLog('🔄 發送設備重啟命令...', 'warning');
            
            try {
                const response = await fetch('http://localhost:8080/api/remote-control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'reset',
                        params: {
                            type: 'Soft'
                        }
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`✅ ${result.message}`, 'success');
                    addLog(`📡 訊息ID: ${result.messageId}`, 'info');
                    
                    // 重啟時清除交易狀態
                    if (currentTransaction) {
                        currentTransaction = null;
                        updateTransactionStatus();
                    }
                } else {
                    addLog(`❌ 重啟設備失敗: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 網路錯誤: ${error.message}`, 'error');
                addLog('💡 請確認OCPP伺服器正在運行', 'info');
            }
        }

        async function getConfiguration() {
            addLog('⚙️ 發送讀取設定命令...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/api/remote-control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'config',
                        params: {
                            key: [] // 讀取所有配置
                        }
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`✅ ${result.message}`, 'success');
                    addLog(`📡 訊息ID: ${result.messageId}`, 'info');
                    addLog('⏳ 等待充電樁回傳配置資料...', 'info');
                } else {
                    addLog(`❌ 讀取設定失敗: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 網路錯誤: ${error.message}`, 'error');
                addLog('💡 請確認OCPP伺服器正在運行', 'info');
            }
        }

        // 請求電錶數據
        async function requestMeterData() {
            addLog('📊 請求電錶數據...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/api/remote-control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'meter',
                        params: {
                            connectorId: 1
                        }
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLog(`✅ ${result.message}`, 'success');
                    addLog(`📡 訊息ID: ${result.messageId}`, 'info');
                } else {
                    addLog(`❌ 請求電錶數據失敗: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 網路錯誤: ${error.message}`, 'error');
                addLog('💡 請確認OCPP伺服器正在運行', 'info');
            }
        }

        // 獲取充電樁狀態
        async function fetchChargePointStatus() {
            try {
                const response = await fetch('http://localhost:8080/api/status');
                const status = await response.json();
                
                if (status.chargePoints && status.chargePoints.length > 0) {
                    const chargePoint = status.chargePoints[0]; // 使用第一個真實充電樁
                    
                    // 更新連接狀態
                    updateConnectionStatus(true);
                    
                    // 更新充電狀態
                    document.getElementById('chargingStatus').textContent = chargePoint.status;
                    document.getElementById('lastHeartbeat').textContent = `${chargePoint.lastHeartbeatAge}秒前`;
                    
                    // 更新電錶數據
                    if (chargePoint.meterData) {
                        updateMeterDataFromAPI(chargePoint.meterData);
                    }
                } else {
                    updateConnectionStatus(false);
                }
            } catch (error) {
                updateConnectionStatus(false);
                console.log('無法獲取充電樁狀態:', error.message);
            }
        }

        // 從API更新電錶數據
        function updateMeterDataFromAPI(meterData) {
            let updated = false;
            
            Object.entries(meterData).forEach(([measurand, data]) => {
                switch (measurand) {
                    case 'Voltage':
                        if (data.value !== realData.voltage) {
                            realData.voltage = parseFloat(data.value);
                            addLog(`📊 電壓更新: ${realData.voltage}${data.unit}`, 'success');
                            updated = true;
                        }
                        break;
                    case 'Current.Import':
                        if (data.value !== realData.current) {
                            realData.current = parseFloat(data.value);
                            addLog(`📊 電流更新: ${realData.current}${data.unit}`, 'success');
                            updated = true;
                        }
                        break;
                    case 'Power.Active.Import':
                        if (data.value !== realData.power) {
                            realData.power = parseFloat(data.value);
                            addLog(`📊 功率更新: ${realData.power}${data.unit}`, 'success');
                            updated = true;
                        }
                        break;
                    case 'Energy.Active.Import.Register':
                        if (data.value !== realData.energy) {
                            realData.energy = parseFloat(data.value);
                            addLog(`📊 電量更新: ${realData.energy}${data.unit}`, 'success');
                            updated = true;
                        }
                        break;
                    case 'Temperature':
                        if (data.value !== realData.temperature) {
                            realData.temperature = parseFloat(data.value);
                            addLog(`📊 溫度更新: ${realData.temperature}${data.unit}`, 'success');
                            updated = true;
                        }
                        break;
                }
            });
            
            if (updated) {
                // 計算費用
                realData.cost = realData.energy * 4.5;
                updateRealDataDisplay();
            }
        }

        // 更新真實數據顯示
        function updateRealDataDisplay() {
            document.getElementById('voltageValue').textContent = realData.voltage.toFixed(0);
            document.getElementById('currentValue').textContent = realData.current.toFixed(1);
            document.getElementById('powerValue').textContent = realData.power.toFixed(2);
            document.getElementById('energyValue').textContent = realData.energy.toFixed(2);
            document.getElementById('temperatureValue').textContent = realData.temperature.toFixed(0);
            document.getElementById('costValue').textContent = realData.cost.toFixed(2);
            
            // 根據真實數據更新狀態
            if (realData.current > 1) {
                document.getElementById('chargingStatus').textContent = '充電中';
                document.getElementById('chargingStatus').style.color = '#f39c12';
                isCharging = true;
            } else {
                document.getElementById('chargingStatus').textContent = '可用';
                document.getElementById('chargingStatus').style.color = '#27ae60';
                isCharging = false;
            }
        }

        // 更新連接狀態
        function updateConnectionStatus(connected) {
            const indicator = document.getElementById('statusIndicator');
            const status = document.getElementById('connectionStatus');
            
            if (connected) {
                indicator.style.background = '#27ae60';
                status.textContent = '已連接';
                status.style.color = '#27ae60';
                addLog('✅ 真實充電樁 20241024NJ1 已連接', 'success');
            } else {
                indicator.style.background = '#e74c3c';
                status.textContent = '未連接';
                status.style.color = '#e74c3c';
                addLog('❌ 真實充電樁連接中斷', 'error');
            }
        }

        // 更新交易狀態
        function updateTransactionStatus() {
            const card = document.getElementById('transactionCard');
            const status = document.getElementById('transactionStatus');
            
            if (currentTransaction) {
                card.classList.add('transaction-active');
                status.innerHTML = `
                    交易 ID: ${currentTransaction.id}<br>
                    開始時間: ${currentTransaction.startTime.toLocaleTimeString()}
                `;
            } else {
                card.classList.remove('transaction-active');
                status.textContent = '無進行中交易';
            }
        }

        // 更新連接時間
        function updateConnectionTime() {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const hours = Math.floor(elapsed / 3600);
            const minutes = Math.floor((elapsed % 3600) / 60);
            const seconds = elapsed % 60;
            
            document.getElementById('connectionTime').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 添加日誌
        function addLog(message, type = 'info') {
            const logContent = document.getElementById('logContent');
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${time}] ${getLogIcon(type)} ${message}`;
            
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
            
            // 限制日誌條目數量
            const entries = logContent.getElementsByClassName('log-entry');
            if (entries.length > 100) {
                entries[0].remove();
            }
        }

        function getLogIcon(type) {
            const icons = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌'
            };
            return icons[type] || 'ℹ️';
        }

        // 清除日誌
        function clearLogs() {
            document.getElementById('logContent').innerHTML = '';
            addLog('日誌已清除', 'info');
        }

        // 初始化
        function init() {
            // 檢查伺服器狀態
            checkServerStatus();
            
            // 更新時間顯示
            setInterval(updateConnectionTime, 1000);
            
            // 定期更新連接狀態 (基於真實情況)
            setInterval(() => {
                // 模擬檢查真實連接狀態
                const heartbeatAge = Math.floor(Math.random() * 30) + 1; // 1-30秒前
                document.getElementById('lastHeartbeat').textContent = heartbeatAge + '秒前';
            }, 10000);
            
            // 定期提醒用戶如何獲取真實數據
            setInterval(() => {
                if (realData.current === 0) {
                    addLog('💡 提示：真實充電樁未充電或未發送電錶數據', 'info');
                    addLog('📊 請在OCPP伺服器終端輸入 meter 請求數據', 'info');
                }
            }, 60000); // 每分鐘提醒一次
            
            // 初始化顯示
            updateRealDataDisplay();
        }

        // 頁面加載時初始化
        window.addEventListener('load', init);
    </script>
</body>
</html> 