const WebSocket = require('ws');
const http = require('http');

console.log('🔧 OCPP 連接修復工具');
console.log('='.repeat(50));

// 根據搜尋結果，充電樁連接後立即斷開的常見原因：
// 1. BootNotification回應格式不正確
// 2. 缺少必要的OCPP協定支援
// 3. WebSocket子協定不匹配
// 4. 訊息格式不符合OCPP標準

class OCPPConnectionFixer {
    constructor() {
        this.server = null;
        this.connectedChargers = new Map();
        this.messageQueue = new Map();
    }

    startServer(port = 8080) {
        console.log(`\n🚀 啟動修復版OCPP伺服器 (端口: ${port})`);
        
        // 創建HTTP伺服器
        this.server = http.createServer();
        
        // 創建WebSocket伺服器，支援多種OCPP版本
        const wss = new WebSocket.Server({
            server: this.server,
            verifyClient: (info) => {
                const protocol = info.req.headers['sec-websocket-protocol'];
                const url = info.req.url;
                
                console.log(`\n📡 收到連接請求:`);
                console.log(`   URL: ${url}`);
                console.log(`   協定: ${protocol}`);
                console.log(`   來源IP: ${info.req.connection.remoteAddress}`);
                
                // 檢查是否支援OCPP協定
                if (!protocol || !protocol.includes('ocpp')) {
                    console.log(`❌ 不支援的協定: ${protocol}`);
                    return false;
                }
                
                return true;
            }
        });

        wss.on('connection', (ws, req) => {
            this.handleConnection(ws, req);
        });

        this.server.listen(port, () => {
            console.log(`✅ OCPP修復伺服器已啟動在端口 ${port}`);
            console.log(`   監聽地址: ws://*************:${port}`);
            console.log(`   支援協定: ocpp1.6, ocpp2.0, ocpp2.0.1`);
        });
    }

    handleConnection(ws, req) {
        const url = req.url;
        const protocol = req.headers['sec-websocket-protocol'];
        const clientIP = req.connection.remoteAddress;
        
        // 從URL中提取充電樁ID
        const chargePointId = this.extractChargePointId(url);
        
        console.log(`\n✅ 新連接建立:`);
        console.log(`   充電樁ID: ${chargePointId}`);
        console.log(`   協定版本: ${protocol}`);
        console.log(`   客戶端IP: ${clientIP}`);

        // 儲存連接信息
        this.connectedChargers.set(chargePointId, {
            ws: ws,
            protocol: protocol,
            ip: clientIP,
            connectedAt: new Date(),
            lastSeen: new Date()
        });

        // 設置連接事件處理
        ws.on('message', (data) => {
            this.handleMessage(ws, chargePointId, data);
        });

        ws.on('close', (code, reason) => {
            console.log(`\n❌ 充電樁 ${chargePointId} 連接中斷:`);
            console.log(`   關閉代碼: ${code}`);
            console.log(`   原因: ${reason || '未知'}`);
            this.connectedChargers.delete(chargePointId);
        });

        ws.on('error', (error) => {
            console.log(`\n🚨 充電樁 ${chargePointId} 連接錯誤:`);
            console.log(`   錯誤: ${error.message}`);
        });

        // 發送歡迎訊息（可選）
        console.log(`\n🎉 充電樁 ${chargePointId} 已成功連接！等待BootNotification...`);
    }

    extractChargePointId(url) {
        // 從URL路徑中提取充電樁ID
        const parts = url.split('/');
        return parts[parts.length - 1] || 'UNKNOWN';
    }

    handleMessage(ws, chargePointId, data) {
        try {
            const message = JSON.parse(data.toString());
            const messageType = message[0];
            const messageId = message[1];
            const action = message[2];
            const payload = message[3];

            console.log(`\n📥 收到來自 ${chargePointId} 的訊息:`);
            console.log(`   類型: ${messageType} (${this.getMessageTypeName(messageType)})`);
            console.log(`   ID: ${messageId}`);
            console.log(`   動作: ${action}`);
            console.log(`   內容: ${JSON.stringify(payload, null, 2)}`);

            // 更新最後活動時間
            const charger = this.connectedChargers.get(chargePointId);
            if (charger) {
                charger.lastSeen = new Date();
            }

            // 根據訊息類型處理
            if (messageType === 2) { // CALL
                this.handleCall(ws, chargePointId, messageId, action, payload);
            } else if (messageType === 3) { // CALLRESULT
                this.handleCallResult(ws, chargePointId, messageId, payload);
            } else if (messageType === 4) { // CALLERROR
                this.handleCallError(ws, chargePointId, messageId, payload);
            }

        } catch (error) {
            console.log(`\n❌ 解析訊息失敗 (${chargePointId}): ${error.message}`);
            this.sendCallError(ws, 'UNKNOWN', 'FormatViolation', 
                '訊息格式錯誤', {});
        }
    }

    handleCall(ws, chargePointId, messageId, action, payload) {
        console.log(`\n🔄 處理 ${action} 請求...`);

        switch (action) {
            case 'BootNotification':
                this.handleBootNotification(ws, chargePointId, messageId, payload);
                break;
            case 'Heartbeat':
                this.handleHeartbeat(ws, chargePointId, messageId, payload);
                break;
            case 'StatusNotification':
                this.handleStatusNotification(ws, chargePointId, messageId, payload);
                break;
            case 'MeterValues':
                this.handleMeterValues(ws, chargePointId, messageId, payload);
                break;
            case 'StartTransaction':
                this.handleStartTransaction(ws, chargePointId, messageId, payload);
                break;
            case 'StopTransaction':
                this.handleStopTransaction(ws, chargePointId, messageId, payload);
                break;
            default:
                console.log(`⚠️  未知動作: ${action}`);
                this.sendCallError(ws, messageId, 'NotImplemented', 
                    `動作 ${action} 尚未實作`, {});
        }
    }

    handleBootNotification(ws, chargePointId, messageId, payload) {
        console.log(`\n🥾 處理BootNotification (${chargePointId})`);
        
        // 創建標準的BootNotification回應
        const response = {
            currentTime: new Date().toISOString(),
            interval: 300, // 5分鐘心跳間隔
            status: 'Accepted'
        };

        this.sendCallResult(ws, messageId, response);
        
        console.log(`✅ BootNotification已接受 (${chargePointId})`);
        console.log(`   當前時間: ${response.currentTime}`);
        console.log(`   心跳間隔: ${response.interval}秒`);
    }

    handleHeartbeat(ws, chargePointId, messageId, payload) {
        console.log(`\n💓 處理Heartbeat (${chargePointId})`);
        
        const response = {
            currentTime: new Date().toISOString()
        };

        this.sendCallResult(ws, messageId, response);
        console.log(`✅ Heartbeat回應已發送 (${chargePointId})`);
    }

    handleStatusNotification(ws, chargePointId, messageId, payload) {
        console.log(`\n📊 處理StatusNotification (${chargePointId})`);
        console.log(`   連接器ID: ${payload.connectorId}`);
        console.log(`   狀態: ${payload.status}`);
        console.log(`   錯誤代碼: ${payload.errorCode || '無'}`);
        
        this.sendCallResult(ws, messageId, {});
        console.log(`✅ StatusNotification回應已發送 (${chargePointId})`);
    }

    handleMeterValues(ws, chargePointId, messageId, payload) {
        console.log(`\n⚡ 處理MeterValues (${chargePointId})`);
        console.log(`   連接器ID: ${payload.connectorId}`);
        console.log(`   電錶數據: ${JSON.stringify(payload.meterValue, null, 2)}`);
        
        this.sendCallResult(ws, messageId, {});
        console.log(`✅ MeterValues回應已發送 (${chargePointId})`);
    }

    handleStartTransaction(ws, chargePointId, messageId, payload) {
        console.log(`\n🚀 處理StartTransaction (${chargePointId})`);
        
        const response = {
            idTagInfo: {
                status: 'Accepted'
            },
            transactionId: Math.floor(Math.random() * 1000000)
        };

        this.sendCallResult(ws, messageId, response);
        console.log(`✅ StartTransaction已接受 (${chargePointId})`);
        console.log(`   交易ID: ${response.transactionId}`);
    }

    handleStopTransaction(ws, chargePointId, messageId, payload) {
        console.log(`\n🛑 處理StopTransaction (${chargePointId})`);
        
        const response = {
            idTagInfo: {
                status: 'Accepted'
            }
        };

        this.sendCallResult(ws, messageId, response);
        console.log(`✅ StopTransaction已接受 (${chargePointId})`);
    }

    sendCallResult(ws, messageId, payload) {
        const response = [3, messageId, payload];
        const responseStr = JSON.stringify(response);
        
        console.log(`📤 發送CALLRESULT: ${responseStr}`);
        ws.send(responseStr);
    }

    sendCallError(ws, messageId, errorCode, errorDescription, errorDetails) {
        const response = [4, messageId, errorCode, errorDescription, errorDetails];
        const responseStr = JSON.stringify(response);
        
        console.log(`📤 發送CALLERROR: ${responseStr}`);
        ws.send(responseStr);
    }

    getMessageTypeName(type) {
        switch (type) {
            case 2: return 'CALL';
            case 3: return 'CALLRESULT';
            case 4: return 'CALLERROR';
            default: return 'UNKNOWN';
        }
    }

    showStatus() {
        console.log(`\n📊 伺服器狀態:`);
        console.log(`   已連接充電樁數量: ${this.connectedChargers.size}`);
        
        if (this.connectedChargers.size > 0) {
            console.log(`\n📋 已連接的充電樁:`);
            this.connectedChargers.forEach((charger, id) => {
                const uptime = Math.floor((Date.now() - charger.connectedAt.getTime()) / 1000);
                console.log(`   • ${id}:`);
                console.log(`     IP: ${charger.ip}`);
                console.log(`     協定: ${charger.protocol}`);
                console.log(`     連接時間: ${uptime}秒`);
                console.log(`     最後活動: ${charger.lastSeen.toLocaleTimeString()}`);
            });
        }
    }

    stop() {
        if (this.server) {
            this.server.close();
            console.log('\n🛑 OCPP修復伺服器已停止');
        }
    }
}

// 啟動修復工具
const fixer = new OCPPConnectionFixer();
fixer.startServer(8080);

// 每30秒顯示狀態
setInterval(() => {
    fixer.showStatus();
}, 30000);

// 處理程序退出
process.on('SIGINT', () => {
    console.log('\n\n👋 正在關閉OCPP修復伺服器...');
    fixer.stop();
    process.exit(0);
});

console.log('\n📝 使用說明:');
console.log('1. 將充電樁的Server URL設定為: ws://*************:8080/20241024NJ1');
console.log('2. 點擊充電樁的"Save and RST"按鈕');
console.log('3. 觀察此終端的輸出，查看連接和訊息處理情況');
console.log('4. 按 Ctrl+C 停止伺服器');
console.log('\n⏳ 等待充電樁連接...'); 