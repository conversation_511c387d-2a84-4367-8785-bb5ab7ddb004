<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCPP 遠端控制測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .card h3 {
            margin-bottom: 15px;
            color: #333;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; transform: translateY(-1px); }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input, .input-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .status {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-connected { background: #28a745; }
        .status-disconnected { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 OCPP 遠端控制測試面板</h1>
            <div style="margin-top: 10px;">
                <span class="status" id="status"></span>
                <span id="statusText">檢查連接中...</span>
            </div>
        </div>

        <div class="control-panel">
            <div class="card">
                <h3>📋 充電樁選擇</h3>
                <div class="input-group">
                    <label>選擇充電樁:</label>
                    <select id="chargerSelect">
                        <option value="">載入中...</option>
                    </select>
                </div>
                <button class="btn btn-primary" onclick="refreshChargers()">🔄 重新整理</button>
            </div>

            <div class="card">
                <h3>⚙️ 控制參數</h3>
                <div class="input-group">
                    <label>連接器編號:</label>
                    <input type="number" id="connectorId" value="1" min="1">
                </div>
                <div class="input-group">
                    <label>ID標籤:</label>
                    <input type="text" id="idTag" value="REMOTE_TEST">
                </div>
            </div>
        </div>

        <div class="card" style="margin-bottom: 20px;">
            <h3>🎯 遠端控制命令</h3>
            <button class="btn btn-success" onclick="startCharging()" id="startBtn" disabled>🚗 啟動充電</button>
            <button class="btn btn-warning" onclick="stopCharging()" id="stopBtn" disabled>🛑 停止充電</button>
            <button class="btn btn-danger" onclick="resetCharger()" id="resetBtn" disabled>🔄 重啟充電樁</button>
            <button class="btn btn-primary" onclick="getConfig()" id="configBtn" disabled>⚙️ 讀取設定</button>
            <button class="btn btn-primary" onclick="requestMeter()" id="meterBtn" disabled>📊 請求電錶數據</button>
        </div>

        <div class="card">
            <h3>📝 操作日誌</h3>
            <div id="logContainer" class="log-container">
                [系統] 遠端控制測試面板已就緒<br>
            </div>
            <button class="btn btn-warning" onclick="clearLog()" style="margin-top: 10px;">🗑️ 清除日誌</button>
        </div>
    </div>

    <script>
        let selectedCharger = null;
        const serverUrl = 'http://localhost:8080';

        function log(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#00ff00',
                'success': '#00ff00',
                'error': '#ff0000',
                'warning': '#ffff00',
                'send': '#ff8800',
                'receive': '#00ffff'
            };
            
            const color = colors[type] || '#00ff00';
            container.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            container.scrollTop = container.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '[系統] 日誌已清除<br>';
        }

        async function refreshChargers() {
            try {
                log('正在刷新充電樁列表...', 'info');
                
                const response = await fetch(`${serverUrl}/api/status`);
                const data = await response.json();
                
                const select = document.getElementById('chargerSelect');
                select.innerHTML = '<option value="">請選擇充電樁...</option>';
                
                if (data.connectedChargePoints && data.connectedChargePoints.length > 0) {
                    data.connectedChargePoints.forEach(cp => {
                        const option = document.createElement('option');
                        option.value = cp.id;
                        option.textContent = `${cp.id} (${cp.status})`;
                        select.appendChild(option);
                    });
                    log(`發現 ${data.connectedChargePoints.length} 個已連接的充電樁`, 'success');
                    updateStatus(true, `發現 ${data.connectedChargePoints.length} 個充電樁`);
                } else {
                    log('未發現已連接的充電樁', 'warning');
                    updateStatus(false, '無充電樁連接');
                }
            } catch (error) {
                log(`刷新充電樁列表失敗: ${error.message}`, 'error');
                updateStatus(false, '連接失敗');
            }
        }

        function updateStatus(connected, text) {
            const status = document.getElementById('status');
            const statusText = document.getElementById('statusText');
            
            status.className = connected ? 'status status-connected' : 'status status-disconnected';
            statusText.textContent = text;
        }

        document.getElementById('chargerSelect').addEventListener('change', function(e) {
            selectedCharger = e.target.value;
            const buttons = ['startBtn', 'stopBtn', 'resetBtn', 'configBtn', 'meterBtn'];
            
            if (selectedCharger) {
                log(`已選擇充電樁: ${selectedCharger}`, 'info');
                buttons.forEach(btnId => {
                    document.getElementById(btnId).disabled = false;
                });
            } else {
                buttons.forEach(btnId => {
                    document.getElementById(btnId).disabled = true;
                });
            }
        });

        async function sendCommand(action, payload = {}) {
            if (!selectedCharger) {
                log('請先選擇充電樁', 'error');
                return;
            }

            try {
                log(`發送命令: ${action} 給 ${selectedCharger}`, 'send');
                
                const response = await fetch(`${serverUrl}/api/remote-control`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        chargePointId: selectedCharger,
                        action: action,
                        ...payload
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    log(`命令執行成功: ${result.message || '操作完成'}`, 'success');
                } else {
                    log(`命令執行失敗: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`發送命令失敗: ${error.message}`, 'error');
            }
        }

        function startCharging() {
            const connectorId = parseInt(document.getElementById('connectorId').value) || 1;
            const idTag = document.getElementById('idTag').value || 'REMOTE_TEST';
            
            sendCommand('RemoteStartTransaction', {
                connectorId: connectorId,
                idTag: idTag
            });
        }

        function stopCharging() {
            sendCommand('RemoteStopTransaction', {
                transactionId: Math.floor(Math.random() * 10000)
            });
        }

        function resetCharger() {
            sendCommand('Reset', {
                type: 'Soft'
            });
        }

        function getConfig() {
            sendCommand('GetConfiguration');
        }

        function requestMeter() {
            const connectorId = parseInt(document.getElementById('connectorId').value) || 1;
            sendCommand('TriggerMessage', {
                requestedMessage: 'MeterValues',
                connectorId: connectorId
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('系統初始化...', 'info');
            refreshChargers();
        });
    </script>
</body>
</html> 