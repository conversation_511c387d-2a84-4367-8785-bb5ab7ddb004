* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

.container {
    background-color: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    font-weight: 600;
}

h2 {
    color: #555;
    margin: 1.5rem 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #444;
}

input[type="text"] {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.button-group {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
}

button {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

#connect-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
}

#connect-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

#disconnect-btn {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

#disconnect-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.status-disconnected {
    text-align: center;
    padding: 0.75rem;
    margin-bottom: 1.5rem;
    border-radius: 6px;
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #333;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.status-connected {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    text-align: center;
    padding: 0.75rem;
    margin-bottom: 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.control-panel {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
    border: 1px solid #e9ecef;
}

.control-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.control-buttons button {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    font-size: 0.9rem;
    padding: 0.6rem 1rem;
}

.control-buttons button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

.log-container {
    margin-top: 2rem;
}

.log-controls {
    margin-bottom: 1rem;
}

#clear-log-btn {
    background: linear-gradient(135deg, #6c757d, #adb5bd);
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

#clear-log-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

#message-log {
    border: 2px solid #e9ecef;
    height: 250px;
    overflow-y: auto;
    padding: 1rem;
    background-color: #f8f9fa;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    border-radius: 6px;
}

#message-log::-webkit-scrollbar {
    width: 8px;
}

#message-log::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#message-log::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

#message-log::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        padding: 1.5rem;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .control-buttons {
        grid-template-columns: 1fr;
    }
} 