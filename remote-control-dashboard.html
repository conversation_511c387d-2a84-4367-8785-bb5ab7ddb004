<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCPP 遠端控制測試面板</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .content {
            padding: 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e0e0e0;
            margin-bottom: 1rem;
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .control-section {
            display: grid;
            gap: 1rem;
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .input-group label {
            font-weight: 600;
            color: #495057;
        }

        .input-group input, .input-group select {
            padding: 0.8rem;
            border: 1px solid #ced4da;
            border-radius: 8px;
            font-size: 1rem;
        }

        .btn {
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            margin: 0.5rem 0;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .log-section {
            grid-column: 1 / -1;
        }

        .log-container {
            background: #1a1a1a;
            color: #ffffff;
            padding: 1rem;
            border-radius: 8px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.3rem 0;
        }

        .log-info { color: #17a2b8; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-send { color: #fd7e14; }
        .log-receive { color: #20c997; }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #6c757d;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-connected {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-disconnected {
            background: #dc3545;
        }

        .status-testing {
            background: #ffc107;
            animation: blink 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .charger-select {
            width: 100%;
            margin-bottom: 1rem;
        }

        .test-results {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            margin: 0.3rem 0;
            border-radius: 5px;
            background: white;
        }

        .result-pass { border-left: 4px solid #28a745; }
        .result-fail { border-left: 4px solid #dc3545; }
        .result-pending { border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🎮 OCPP 遠端控制測試面板</h1>
            <p>測試充電樁的遠端控制功能</p>
        </div>

        <div class="content">
            <!-- 連接狀態 -->
            <div class="card">
                <h3>📡 連接狀態</h3>
                <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                    <span class="status-indicator" id="connectionStatus"></span>
                    <span id="connectionText">未連接</span>
                </div>
                
                <div class="input-group">
                    <label for="chargerSelect">選擇充電樁:</label>
                    <select id="chargerSelect" class="charger-select">
                        <option value="">請選擇充電樁...</option>
                    </select>
                </div>

                <button id="refreshBtn" class="btn btn-info">🔄 重新整理充電樁列表</button>
            </div>

            <!-- 基本控制 -->
            <div class="card">
                <h3>⚡ 基本控制</h3>
                <div class="control-section">
                    <button id="startChargingBtn" class="btn btn-success" disabled>
                        🚗 啟動充電
                    </button>
                    <button id="stopChargingBtn" class="btn btn-warning" disabled>
                        🛑 停止充電
                    </button>
                    <button id="resetBtn" class="btn btn-danger" disabled>
                        🔄 重啟充電樁
                    </button>
                    <button id="getConfigBtn" class="btn btn-info" disabled>
                        ⚙️ 讀取設定
                    </button>
                </div>
            </div>

            <!-- 進階控制 -->
            <div class="card">
                <h3>🔧 進階控制</h3>
                <div class="control-section">
                    <div class="input-group">
                        <label for="connectorId">連接器編號:</label>
                        <input type="number" id="connectorId" value="1" min="1">
                    </div>
                    
                    <div class="input-group">
                        <label for="idTag">ID標籤:</label>
                        <input type="text" id="idTag" value="REMOTE_TEST" placeholder="輸入ID標籤">
                    </div>

                    <div class="input-group">
                        <label for="resetType">重啟類型:</label>
                        <select id="resetType">
                            <option value="Soft">軟重啟</option>
                            <option value="Hard">硬重啟</option>
                        </select>
                    </div>

                    <button id="requestMeterBtn" class="btn btn-primary" disabled>
                        📊 請求電錶數據
                    </button>
                </div>
            </div>

            <!-- 測試結果 -->
            <div class="card">
                <h3>📋 測試結果</h3>
                <div class="test-results" id="testResults">
                    <div class="result-item result-pending">
                        <span>等待測試...</span>
                        <span>⏳</span>
                    </div>
                </div>
                <button id="runAllTestsBtn" class="btn btn-primary">
                    🧪 執行完整測試
                </button>
            </div>

            <!-- 日誌顯示 -->
            <div class="card log-section">
                <h3>📝 操作日誌</h3>
                <div id="logContainer" class="log-container">
                    <div class="log-entry log-info">[系統] 遠端控制測試面板已就緒</div>
                </div>
                <button id="clearLogBtn" class="btn btn-warning" style="margin-top: 1rem;">
                    🗑️ 清除日誌
                </button>
            </div>
        </div>
    </div>

    <script>
        class RemoteControlDashboard {
            constructor() {
                this.serverUrl = 'http://localhost:8080';
                this.selectedCharger = null;
                this.testResults = new Map();
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.refreshChargerList();
                this.log('系統初始化完成', 'info');
            }

            setupEventListeners() {
                document.getElementById('refreshBtn').addEventListener('click', () => this.refreshChargerList());
                document.getElementById('chargerSelect').addEventListener('change', (e) => this.selectCharger(e.target.value));
                
                document.getElementById('startChargingBtn').addEventListener('click', () => this.startCharging());
                document.getElementById('stopChargingBtn').addEventListener('click', () => this.stopCharging());
                document.getElementById('resetBtn').addEventListener('click', () => this.resetCharger());
                document.getElementById('getConfigBtn').addEventListener('click', () => this.getConfiguration());
                document.getElementById('requestMeterBtn').addEventListener('click', () => this.requestMeterValues());
                
                document.getElementById('runAllTestsBtn').addEventListener('click', () => this.runAllTests());
                document.getElementById('clearLogBtn').addEventListener('click', () => this.clearLog());
            }

            async refreshChargerList() {
                try {
                    this.log('正在刷新充電樁列表...', 'info');
                    
                    const response = await fetch(`${this.serverUrl}/api/status`);
                    const data = await response.json();
                    
                    const select = document.getElementById('chargerSelect');
                    select.innerHTML = '<option value="">請選擇充電樁...</option>';
                    
                    if (data.connectedChargePoints && data.connectedChargePoints.length > 0) {
                        data.connectedChargePoints.forEach(cp => {
                            const option = document.createElement('option');
                            option.value = cp.id;
                            option.textContent = `${cp.id} (${cp.status}) - ${cp.lastHeartbeat}`;
                            select.appendChild(option);
                        });
                        this.log(`發現 ${data.connectedChargePoints.length} 個已連接的充電樁`, 'success');
                    } else {
                        this.log('未發現已連接的充電樁', 'warning');
                    }
                } catch (error) {
                    this.log(`刷新充電樁列表失敗: ${error.message}`, 'error');
                }
            }

            selectCharger(chargerId) {
                this.selectedCharger = chargerId;
                const buttons = ['startChargingBtn', 'stopChargingBtn', 'resetBtn', 'getConfigBtn', 'requestMeterBtn'];
                
                if (chargerId) {
                    this.log(`已選擇充電樁: ${chargerId}`, 'info');
                    this.updateConnectionStatus(true, `已連接: ${chargerId}`);
                    buttons.forEach(btnId => {
                        document.getElementById(btnId).disabled = false;
                    });
                } else {
                    this.updateConnectionStatus(false, '未選擇充電樁');
                    buttons.forEach(btnId => {
                        document.getElementById(btnId).disabled = true;
                    });
                }
            }

            updateConnectionStatus(connected, text) {
                const indicator = document.getElementById('connectionStatus');
                const statusText = document.getElementById('connectionText');
                
                if (connected) {
                    indicator.className = 'status-indicator status-connected';
                } else {
                    indicator.className = 'status-indicator status-disconnected';
                }
                
                statusText.textContent = text;
            }

            async sendRemoteCommand(action, payload = {}) {
                if (!this.selectedCharger) {
                    this.log('請先選擇充電樁', 'error');
                    return false;
                }

                try {
                    this.log(`發送遠端命令: ${action} 給 ${this.selectedCharger}`, 'send');
                    
                    const response = await fetch(`${this.serverUrl}/api/remote-control`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            chargePointId: this.selectedCharger,
                            action: action,
                            ...payload
                        })
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        this.log(`命令執行成功: ${result.message || '操作完成'}`, 'success');
                        this.recordTestResult(action, true, result.message);
                        return true;
                    } else {
                        this.log(`命令執行失敗: ${result.error}`, 'error');
                        this.recordTestResult(action, false, result.error);
                        return false;
                    }
                } catch (error) {
                    this.log(`發送命令失敗: ${error.message}`, 'error');
                    this.recordTestResult(action, false, error.message);
                    return false;
                }
            }

            async startCharging() {
                const connectorId = parseInt(document.getElementById('connectorId').value) || 1;
                const idTag = document.getElementById('idTag').value || 'REMOTE_TEST';
                
                return await this.sendRemoteCommand('RemoteStartTransaction', {
                    connectorId: connectorId,
                    idTag: idTag
                });
            }

            async stopCharging() {
                return await this.sendRemoteCommand('RemoteStopTransaction', {
                    transactionId: Math.floor(Math.random() * 10000)
                });
            }

            async resetCharger() {
                const resetType = document.getElementById('resetType').value;
                return await this.sendRemoteCommand('Reset', {
                    type: resetType
                });
            }

            async getConfiguration() {
                return await this.sendRemoteCommand('GetConfiguration');
            }

            async requestMeterValues() {
                const connectorId = parseInt(document.getElementById('connectorId').value) || 1;
                return await this.sendRemoteCommand('TriggerMessage', {
                    requestedMessage: 'MeterValues',
                    connectorId: connectorId
                });
            }

            async runAllTests() {
                this.log('🧪 開始執行完整測試套件', 'info');
                this.testResults.clear();
                this.updateTestResults();

                const tests = [
                    { name: 'GetConfiguration', func: () => this.getConfiguration() },
                    { name: 'RemoteStartTransaction', func: () => this.startCharging() },
                    { name: 'TriggerMessage (MeterValues)', func: () => this.requestMeterValues() },
                    { name: 'RemoteStopTransaction', func: () => this.stopCharging() },
                    { name: 'Reset (Soft)', func: () => this.resetCharger() }
                ];

                for (const test of tests) {
                    this.log(`執行測試: ${test.name}`, 'info');
                    await test.func();
                    await this.delay(3000); // 等待3秒再執行下一個測試
                }

                this.log('✅ 完整測試套件執行完成', 'success');
                this.generateTestReport();
            }

            recordTestResult(testName, success, details) {
                this.testResults.set(testName, {
                    success: success,
                    details: details,
                    timestamp: new Date()
                });
                this.updateTestResults();
            }

            updateTestResults() {
                const container = document.getElementById('testResults');
                container.innerHTML = '';

                if (this.testResults.size === 0) {
                    container.innerHTML = '<div class="result-item result-pending"><span>等待測試...</span><span>⏳</span></div>';
                    return;
                }

                this.testResults.forEach((result, testName) => {
                    const item = document.createElement('div');
                    item.className = `result-item ${result.success ? 'result-pass' : 'result-fail'}`;
                    item.innerHTML = `
                        <span>${testName}</span>
                        <span>${result.success ? '✅' : '❌'}</span>
                    `;
                    container.appendChild(item);
                });
            }

            generateTestReport() {
                const total = this.testResults.size;
                const passed = Array.from(this.testResults.values()).filter(r => r.success).length;
                const failed = total - passed;

                this.log('📊 測試報告', 'info');
                this.log(`總測試數: ${total}, 通過: ${passed}, 失敗: ${failed}`, 'info');
                this.log(`成功率: ${total > 0 ? ((passed / total) * 100).toFixed(1) : 0}%`, 'info');
            }

            log(message, type = 'info') {
                const container = document.getElementById('logContainer');
                const entry = document.createElement('div');
                const timestamp = new Date().toLocaleTimeString();
                
                entry.className = `log-entry log-${type}`;
                entry.textContent = `[${timestamp}] ${message}`;
                
                container.appendChild(entry);
                container.scrollTop = container.scrollHeight;
            }

            clearLog() {
                const container = document.getElementById('logContainer');
                container.innerHTML = '<div class="log-entry log-info">[系統] 日誌已清除</div>';
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // 初始化儀表板
        document.addEventListener('DOMContentLoaded', () => {
            new RemoteControlDashboard();
        });
    </script>
</body>
</html> 