<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCPP 伺服器管理介面</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            background-color: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 2rem;
            font-size: 2rem;
            font-weight: 600;
        }

        .status-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .status-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .status-card h2 {
            color: #555;
            margin: 0 0 1rem 0;
            font-size: 1.2rem;
        }

        .server-status {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .connected-chargers {
            background: #e8f5e8;
            border: 1px solid #28a745;
        }

        .charger-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .charger-item {
            background: white;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 6px;
            border-left: 4px solid #28a745;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .charger-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .charger-id {
            font-weight: 600;
            color: #333;
        }

        .charger-details {
            font-size: 0.9rem;
            color: #666;
        }

        .control-section {
            background: #f0f8ff;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            border: 1px solid #b3d9ff;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        button {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #333;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .log-container {
            margin-top: 2rem;
        }

        .log-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        #server-log {
            border: 2px solid #e9ecef;
            height: 400px;
            overflow-y: auto;
            padding: 1rem;
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            border-radius: 6px;
            white-space: pre-wrap;
        }

        .info-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }

        .info-section h3 {
            color: #856404;
            margin-top: 0;
        }

        .info-list {
            color: #856404;
            margin: 0;
        }

        .network-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }

        .network-info code {
            background: #e2e3e5;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: monospace;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .status-section {
                grid-template-columns: 1fr;
            }
            
            .control-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 OCPP 中央系統管理介面</h1>
        
        <div class="status-section">
            <div class="status-card">
                <h2>⚡ 伺服器狀態</h2>
                <div class="server-status">
                    <div class="status-indicator"></div>
                    <span>伺服器運行中 (Port: 8080)</span>
                </div>
                <div>啟動時間: <span id="uptime">計算中...</span></div>
            </div>
            
            <div class="status-card connected-chargers">
                <h2>🔌 已連接充電樁</h2>
                <div>總數量: <span id="charger-count">0</span></div>
                <ul class="charger-list" id="charger-list">
                    <li style="text-align: center; color: #666; padding: 1rem;">
                        暫無充電樁連接
                    </li>
                </ul>
            </div>
        </div>

        <div class="info-section">
            <h3>📋 如何連接您的充電樁</h3>
            <div class="network-info">
                <p><strong>第1步：</strong>找到您電腦的IP位址</p>
                <p>在命令提示字元執行: <code>ipconfig</code></p>
                <p>或在設定中查看網路資訊</p>
            </div>
            
            <div class="network-info">
                <p><strong>第2步：</strong>修改充電樁設定</p>
                <p>將充電樁的 Server URL 改為: <code>ws://您的電腦IP:8080</code></p>
                <p>例如: <code>ws://*************:8080</code></p>
            </div>
            
            <div class="network-info">
                <p><strong>第3步：</strong>重啟充電樁讓設定生效</p>
                <p>充電樁ID保持: <code>20241024NJ1</code></p>
            </div>
        </div>

        <div class="control-section">
            <h2>🎮 遠端控制功能</h2>
            <p>當充電樁連接後，您可以使用以下功能來控制充電樁：</p>
            <div class="control-buttons">
                <button class="btn-primary" onclick="sendRemoteCommand('Reset')">
                    🔄 重啟充電樁
                </button>
                <button class="btn-primary" onclick="sendRemoteCommand('RemoteStartTransaction')">
                    ▶️ 遠端啟動充電
                </button>
                <button class="btn-warning" onclick="sendRemoteCommand('RemoteStopTransaction')">
                    ⏹️ 遠端停止充電
                </button>
                <button class="btn-primary" onclick="sendRemoteCommand('GetConfiguration')">
                    ⚙️ 取得設定
                </button>
            </div>
        </div>

        <div class="log-container">
            <h2>📜 伺服器日誌</h2>
            <div class="log-controls">
                <button class="btn-primary" onclick="clearLog()">清除日誌</button>
                <button class="btn-primary" onclick="refreshLog()">重新整理</button>
            </div>
            <div id="server-log">伺服器日誌將在這裡顯示...</div>
        </div>
    </div>

    <script>
        let startTime = Date.now();
        let logContent = '';

        // 更新運行時間
        function updateUptime() {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const hours = Math.floor(elapsed / 3600);
            const minutes = Math.floor((elapsed % 3600) / 60);
            const seconds = elapsed % 60;
            
            document.getElementById('uptime').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 更新充電樁列表
        function updateChargerList() {
            // 這裡可以連接到實際的伺服器狀態
            // 目前是示例資料
            const chargerListElement = document.getElementById('charger-list');
            const chargerCountElement = document.getElementById('charger-count');
            
            // 示例：如果有連接的充電樁
            // chargerListElement.innerHTML = `
            //     <li class="charger-item">
            //         <div class="charger-info">
            //             <div class="charger-id">20241024NJ1</div>
            //             <div class="charger-details">IP: ************* | 連接時間: 00:05:23</div>
            //         </div>
            //         <span style="color: #28a745;">🟢 在線</span>
            //     </li>
            // `;
            // chargerCountElement.textContent = '1';
        }

        // 發送遠端命令
        function sendRemoteCommand(command) {
            addLog(`📤 發送命令: ${command}`);
            // 這裡可以實作實際的命令發送邏輯
            setTimeout(() => {
                addLog(`✅ 命令 ${command} 已發送`);
            }, 500);
        }

        // 添加日誌
        function addLog(message) {
            const time = new Date().toLocaleTimeString();
            logContent += `[${time}] ${message}\n`;
            
            const logElement = document.getElementById('server-log');
            logElement.textContent = logContent;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 清除日誌
        function clearLog() {
            logContent = '';
            document.getElementById('server-log').textContent = '日誌已清除\n';
            addLog('ℹ️ 日誌系統已重置');
        }

        // 重新整理日誌
        function refreshLog() {
            addLog('🔄 日誌已重新整理');
        }

        // 初始化
        setInterval(updateUptime, 1000);
        setInterval(updateChargerList, 5000);

        // 初始日誌
        addLog('🚀 OCPP 伺服器管理介面已載入');
        addLog('ℹ️ 等待充電樁連接...');
        addLog('💡 請確保充電樁的 Server URL 指向此伺服器');
    </script>
</body>
</html> 