// 等待 HTML 文件完全載入後再執行
document.addEventListener('DOMContentLoaded', () => {

    // 獲取 HTML 元素
    const serverUrlInput = document.getElementById('server-url');
    const chargePointIdInput = document.getElementById('charge-point-id');
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');
    const statusDiv = document.getElementById('connection-status');
    const messageLogDiv = document.getElementById('message-log');
    
    // 控制按鈕
    const bootNotificationBtn = document.getElementById('boot-notification-btn');
    const heartbeatBtn = document.getElementById('heartbeat-btn');
    const statusNotificationBtn = document.getElementById('status-notification-btn');
    const clearLogBtn = document.getElementById('clear-log-btn');

    let websocket = null; // 用於存放 WebSocket 物件
    let messageId = 1; // 訊息 ID 計數器
    let heartbeatInterval = null; // 心跳間隔

    // 記錄日誌的函式
    function log(message, type = 'info') {
        const time = new Date().toLocaleTimeString();
        const typePrefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'send' ? '📤' : type === 'receive' ? '📥' : 'ℹ️';
        messageLogDiv.innerHTML += `[${time}] ${typePrefix} ${message}\n`;
        messageLogDiv.scrollTop = messageLogDiv.scrollHeight; // 自動滾動到底部
    }

    // 更新連線狀態顯示的函式
    function updateStatus(message, isConnected) {
        statusDiv.textContent = message;
        if (isConnected) {
            statusDiv.className = 'status-connected';
            connectBtn.disabled = true;
            disconnectBtn.disabled = false;
            // 啟用控制按鈕
            bootNotificationBtn.disabled = false;
            heartbeatBtn.disabled = false;
            statusNotificationBtn.disabled = false;
        } else {
            statusDiv.className = 'status-disconnected';
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            // 禁用控制按鈕
            bootNotificationBtn.disabled = true;
            heartbeatBtn.disabled = true;
            statusNotificationBtn.disabled = true;
        }
    }

    // 產生唯一訊息ID
    function generateMessageId() {
        return `msg_${Date.now()}_${messageId++}`;
    }

    // 發送 WebSocket 訊息
    function sendMessage(messageType, action, payload) {
        if (!websocket || websocket.readyState !== WebSocket.OPEN) {
            log('錯誤：WebSocket 未連線', 'error');
            return;
        }

        const message = [
            messageType, // 2: CALL, 3: CALLRESULT, 4: CALLERROR
            generateMessageId(),
            action,
            payload
        ];

        const messageString = JSON.stringify(message);
        log(`發送訊息: ${messageString}`, 'send');
        websocket.send(messageString);
    }

    // 處理收到的訊息
    function handleReceivedMessage(data) {
        try {
            const message = JSON.parse(data);
            log(`收到訊息: ${data}`, 'receive');

            // 解析訊息類型
            const [messageType, messageId, actionOrStatus, payload] = message;

            switch (messageType) {
                case 2: // CALL
                    log(`收到 CALL 請求: ${actionOrStatus}`, 'info');
                    handleCallMessage(messageId, actionOrStatus, payload);
                    break;
                case 3: // CALLRESULT
                    log(`收到 CALLRESULT 回應: ${JSON.stringify(payload)}`, 'success');
                    break;
                case 4: // CALLERROR
                    log(`收到 CALLERROR 錯誤: ${actionOrStatus} - ${JSON.stringify(payload)}`, 'error');
                    break;
                default:
                    log(`未知的訊息類型: ${messageType}`, 'error');
            }
        } catch (error) {
            log(`解析訊息失敗: ${error.message}`, 'error');
        }
    }

    // 處理 CALL 訊息（來自伺服器的請求）
    function handleCallMessage(messageId, action, payload) {
        switch (action) {
            case 'Reset':
                // 處理重置請求
                sendCallResult(messageId, { status: 'Accepted' });
                log('處理重置請求', 'info');
                break;
            case 'RemoteStartTransaction':
                // 處理遠端啟動交易請求
                sendCallResult(messageId, { status: 'Accepted' });
                log('處理遠端啟動交易請求', 'info');
                break;
            case 'RemoteStopTransaction':
                // 處理遠端停止交易請求
                sendCallResult(messageId, { status: 'Accepted' });
                log('處理遠端停止交易請求', 'info');
                break;
            default:
                // 未知的動作，回傳錯誤
                sendCallError(messageId, 'NotImplemented', `動作 ${action} 尚未實作`);
                log(`未實作的動作: ${action}`, 'error');
        }
    }

    // 發送 CALLRESULT
    function sendCallResult(messageId, payload) {
        if (!websocket || websocket.readyState !== WebSocket.OPEN) return;
        
        const message = [3, messageId, payload];
        const messageString = JSON.stringify(message);
        log(`發送 CALLRESULT: ${messageString}`, 'send');
        websocket.send(messageString);
    }

    // 發送 CALLERROR
    function sendCallError(messageId, errorCode, errorDescription) {
        if (!websocket || websocket.readyState !== WebSocket.OPEN) return;
        
        const message = [4, messageId, errorCode, errorDescription, {}];
        const messageString = JSON.stringify(message);
        log(`發送 CALLERROR: ${messageString}`, 'send');
        websocket.send(messageString);
    }

    // 自動發送心跳
    function startHeartbeat() {
        // 每30秒發送一次心跳
        heartbeatInterval = setInterval(() => {
            sendMessage(2, 'Heartbeat', {});
        }, 30000);
    }

    function stopHeartbeat() {
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
            heartbeatInterval = null;
        }
    }

    // --- 連線按鈕的點擊事件 ---
    connectBtn.addEventListener('click', () => {
        const serverUrl = serverUrlInput.value.trim();
        const chargePointId = chargePointIdInput.value.trim();

        if (!serverUrl || !chargePointId) {
            alert('Server URL 和 Charge Point ID 皆為必填項目！');
            return;
        }

        // 根據 OCPP 規範，完整的連線 URL 是 Server URL 後面跟著 Charge Point ID
        const fullUrl = `${serverUrl}/${chargePointId}`;
        log(`正在嘗試連線至: ${fullUrl}`, 'info');

        // 建立一個新的 WebSocket 連線
        // 第二個參數 'ocpp1.6' 是必要的協定宣告
        websocket = new WebSocket(fullUrl, 'ocpp1.6');

        // --- WebSocket 事件處理 ---

        // 1. 當連線成功建立時
        websocket.onopen = (event) => {
            log('連線成功！WebSocket 連線已開啟。', 'success');
            updateStatus('已連線 (Connected)', true);
            
            // 連線成功後自動發送 BootNotification
            setTimeout(() => {
                sendBootNotification();
            }, 1000);
            
            // 開始心跳
            startHeartbeat();
        };

        // 2. 當收到從伺服器來的訊息時
        websocket.onmessage = (event) => {
            handleReceivedMessage(event.data);
        };

        // 3. 當連線發生錯誤時
        websocket.onerror = (event) => {
            log('發生錯誤！請檢查 Server URL 是否正確或網路是否通暢。', 'error');
            console.error('WebSocket Error:', event);
        };

        // 4. 當連線關閉時
        websocket.onclose = (event) => {
            log(`連線已關閉。 Code: ${event.code}, Reason: ${event.reason || '無特定原因'}`, 'info');
            updateStatus('未連線 (Disconnected)', false);
            stopHeartbeat();
            websocket = null;
        };
    });

    // --- 斷線按鈕的點擊事件 ---
    disconnectBtn.addEventListener('click', () => {
        if (websocket) {
            websocket.close();
        }
    });

    // --- BootNotification 按鈕事件 ---
    bootNotificationBtn.addEventListener('click', () => {
        sendBootNotification();
    });

    // 發送 BootNotification 訊息
    function sendBootNotification() {
        const payload = {
            chargePointVendor: "AFAX POWER",
            chargePointModel: "AC-Charger-V1",
            chargePointSerialNumber: chargePointIdInput.value,
            firmwareVersion: "1.0.0",
            iccid: "",
            imsi: "",
            meterSerialNumber: "",
            meterType: "Energy Meter"
        };
        
        sendMessage(2, 'BootNotification', payload);
    }

    // --- Heartbeat 按鈕事件 ---
    heartbeatBtn.addEventListener('click', () => {
        sendMessage(2, 'Heartbeat', {});
    });

    // --- StatusNotification 按鈕事件 ---
    statusNotificationBtn.addEventListener('click', () => {
        const payload = {
            connectorId: 1,
            errorCode: "NoError",
            status: "Available",
            timestamp: new Date().toISOString()
        };
        
        sendMessage(2, 'StatusNotification', payload);
    });

    // --- 清除日誌按鈕事件 ---
    clearLogBtn.addEventListener('click', () => {
        messageLogDiv.innerHTML = '';
        log('日誌已清除', 'info');
    });

    // 初始化日誌
    log('OCPP 網頁客戶端已準備就緒', 'success');
}); 