#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python OCPP 充電樁連接測試工具
支援多種連接方式和協定版本
"""

import asyncio
import websockets
import json
import time
import logging
from datetime import datetime
import sys

# 設定日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OCPPTester:
    def __init__(self, server_url, charge_point_id):
        self.server_url = server_url
        self.charge_point_id = charge_point_id
        self.websocket = None
        self.message_id = 1
        self.connected = False
        
    def get_next_message_id(self):
        """獲取下一個訊息ID"""
        msg_id = f"python_test_{int(time.time() * 1000)}_{self.message_id}"
        self.message_id += 1
        return msg_id
        
    async def connect_and_test(self):
        """連接到OCPP伺服器並執行測試"""
        uri = f"{self.server_url}/{self.charge_point_id}"
        
        # 嘗試多種協定版本
        protocols = ["ocpp1.6", "ocpp2.0", "ocpp2.0.1"]
        
        for protocol in protocols:
            try:
                logger.info(f"🔍 嘗試使用協定: {protocol}")
                logger.info(f"📡 連接到: {uri}")
                
                # 建立WebSocket連接
                async with websockets.connect(
                    uri, 
                    subprotocols=[protocol],
                    ping_interval=20,
                    ping_timeout=10,
                    close_timeout=10
                ) as websocket:
                    
                    self.websocket = websocket
                    self.connected = True
                    
                    logger.info(f"✅ 成功連接! 使用協定: {websocket.subprotocol}")
                    logger.info(f"🔗 連接狀態: {websocket.state}")
                    
                    # 執行OCPP測試序列
                    await self.run_ocpp_tests()
                    
                    # 保持連接並監聽訊息
                    await self.listen_for_messages()
                    
            except websockets.exceptions.ConnectionClosedError as e:
                logger.error(f"❌ 連接被關閉 ({protocol}): {e}")
                await self.analyze_connection_error(e)
            except websockets.exceptions.InvalidStatusCode as e:
                logger.error(f"❌ 無效狀態碼 ({protocol}): {e}")
            except websockets.exceptions.InvalidHandshake as e:
                logger.error(f"❌ 握手失敗 ({protocol}): {e}")
            except Exception as e:
                logger.error(f"❌ 連接失敗 ({protocol}): {e}")
                
        logger.error("🚫 所有協定都無法連接")
        
    async def run_ocpp_tests(self):
        """執行OCPP測試序列"""
        logger.info("🚀 開始OCPP測試序列")
        
        # 1. 發送BootNotification
        await self.send_boot_notification()
        await asyncio.sleep(2)
        
        # 2. 發送StatusNotification
        await self.send_status_notification()
        await asyncio.sleep(2)
        
        # 3. 發送Heartbeat
        await self.send_heartbeat()
        await asyncio.sleep(2)
        
        # 4. 發送MeterValues
        await self.send_meter_values()
        
    async def send_boot_notification(self):
        """發送BootNotification"""
        message = [
            2,  # CALL
            self.get_next_message_id(),
            "BootNotification",
            {
                "chargePointVendor": "Python_Test",
                "chargePointModel": "Test_Model_V1",
                "chargePointSerialNumber": "PYTHON_TEST_001",
                "firmwareVersion": "1.0.0-python",
                "chargePointId": self.charge_point_id
            }
        ]
        await self.send_message(message, "BootNotification")
        
    async def send_status_notification(self):
        """發送StatusNotification"""
        message = [
            2,  # CALL
            self.get_next_message_id(),
            "StatusNotification",
            {
                "connectorId": 1,
                "errorCode": "NoError",
                "status": "Available",
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
        ]
        await self.send_message(message, "StatusNotification")
        
    async def send_heartbeat(self):
        """發送Heartbeat"""
        message = [
            2,  # CALL
            self.get_next_message_id(),
            "Heartbeat",
            {}
        ]
        await self.send_message(message, "Heartbeat")
        
    async def send_meter_values(self):
        """發送MeterValues"""
        message = [
            2,  # CALL
            self.get_next_message_id(),
            "MeterValues",
            {
                "connectorId": 1,
                "meterValue": [
                    {
                        "timestamp": datetime.utcnow().isoformat() + "Z",
                        "sampledValue": [
                            {"value": "230.5", "measurand": "Voltage", "unit": "V"},
                            {"value": "15.2", "measurand": "Current.Import", "unit": "A"},
                            {"value": "3.5", "measurand": "Power.Active.Import", "unit": "kW"},
                            {"value": "1.25", "measurand": "Energy.Active.Import.Register", "unit": "kWh"},
                            {"value": "28", "measurand": "Temperature", "unit": "Celsius"}
                        ]
                    }
                ]
            }
        ]
        await self.send_message(message, "MeterValues")
        
    async def send_message(self, message, action):
        """發送訊息到伺服器"""
        try:
            message_str = json.dumps(message)
            logger.info(f"📤 發送 {action}: {message_str}")
            await self.websocket.send(message_str)
        except Exception as e:
            logger.error(f"❌ 發送 {action} 失敗: {e}")
            
    async def listen_for_messages(self):
        """監聽來自伺服器的訊息"""
        logger.info("👂 開始監聽伺服器訊息...")
        
        try:
            async for message in self.websocket:
                await self.handle_received_message(message)
        except websockets.exceptions.ConnectionClosed as e:
            logger.warning(f"⚠️ 連接已關閉: {e}")
        except Exception as e:
            logger.error(f"❌ 監聽訊息時發生錯誤: {e}")
            
    async def handle_received_message(self, message):
        """處理收到的訊息"""
        try:
            data = json.loads(message)
            message_type = data[0]
            
            if message_type == 3:  # CALLRESULT
                message_id = data[1]
                payload = data[2]
                logger.info(f"✅ 收到回應 (ID: {message_id}): {json.dumps(payload, indent=2)}")
                
            elif message_type == 4:  # CALLERROR
                message_id = data[1]
                error_code = data[2]
                error_description = data[3]
                logger.error(f"❌ 收到錯誤 (ID: {message_id}): {error_code} - {error_description}")
                
            elif message_type == 2:  # CALL (來自伺服器的請求)
                message_id = data[1]
                action = data[2]
                payload = data[3]
                logger.info(f"📥 收到伺服器請求: {action}")
                await self.handle_server_request(message_id, action, payload)
                
        except json.JSONDecodeError as e:
            logger.error(f"❌ 無法解析訊息: {message} - {e}")
        except Exception as e:
            logger.error(f"❌ 處理訊息時發生錯誤: {e}")
            
    async def handle_server_request(self, message_id, action, payload):
        """處理來自伺服器的請求"""
        logger.info(f"🔄 處理伺服器請求: {action}")
        
        if action == "Reset":
            # 回應Reset請求
            response = [3, message_id, {"status": "Accepted"}]
            await self.websocket.send(json.dumps(response))
            logger.info("✅ 已回應Reset請求")
            
        elif action == "RemoteStartTransaction":
            # 回應遠端啟動充電請求
            response = [3, message_id, {"status": "Accepted"}]
            await self.websocket.send(json.dumps(response))
            logger.info("✅ 已回應RemoteStartTransaction請求")
            
        elif action == "RemoteStopTransaction":
            # 回應遠端停止充電請求
            response = [3, message_id, {"status": "Accepted"}]
            await self.websocket.send(json.dumps(response))
            logger.info("✅ 已回應RemoteStopTransaction請求")
            
        else:
            # 未知請求，回應NotImplemented錯誤
            error_response = [4, message_id, "NotImplemented", f"Action {action} not implemented", {}]
            await self.websocket.send(json.dumps(error_response))
            logger.warning(f"⚠️ 未實作的請求: {action}")
            
    async def analyze_connection_error(self, error):
        """分析連接錯誤"""
        logger.info("🔬 分析連接錯誤...")
        
        if hasattr(error, 'code'):
            if error.code == 1006:
                logger.info("💡 錯誤代碼1006: 異常關閉")
                logger.info("   可能原因:")
                logger.info("   • 伺服器主動關閉連接")
                logger.info("   • 網路連接問題")
                logger.info("   • 協定不匹配")
            elif error.code == 1002:
                logger.info("💡 錯誤代碼1002: 協定錯誤")
                logger.info("   可能原因:")
                logger.info("   • OCPP協定版本不支援")
                logger.info("   • 訊息格式錯誤")
        
        logger.info("🔧 建議解決方案:")
        logger.info("   1. 檢查伺服器是否正在運行")
        logger.info("   2. 確認網路連接正常")
        logger.info("   3. 嘗試不同的協定版本")
        logger.info("   4. 檢查充電樁設定")

async def main():
    """主函數"""
    # 設定參數
    SERVER_URL = "ws://192.168.0.179:8080"
    CHARGE_POINT_ID = "20241024NJ1"
    
    print("🐍 Python OCPP 充電樁連接測試工具")
    print("=" * 50)
    print(f"📡 伺服器: {SERVER_URL}")
    print(f"🔌 充電樁ID: {CHARGE_POINT_ID}")
    print()
    
    # 建立測試器
    tester = OCPPTester(SERVER_URL, CHARGE_POINT_ID)
    
    try:
        # 執行連接測試
        await tester.connect_and_test()
    except KeyboardInterrupt:
        logger.info("🛑 用戶中斷測試")
    except Exception as e:
        logger.error(f"❌ 測試過程中發生錯誤: {e}")
    finally:
        logger.info("🏁 測試結束")

if __name__ == "__main__":
    # 檢查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 運行測試
    asyncio.run(main()) 