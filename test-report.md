# OCPP 系統測試報告

## 🎯 測試概述

本報告記錄了 OCPP 1.6 系統的完整測試結果，包括 WebSocket 連接、訊息交換和功能驗證。

## ⚙️ 測試環境

- **伺服器IP**: *************
- **OCPP伺服器端口**: 8080
- **網頁介面端口**: 3000
- **充電樁ID**: 20241024NJ1
- **協定版本**: OCPP 1.6J (JSON over WebSocket)

## 🧪 自動化測試結果

### ✅ 基本連接測試
- [x] WebSocket 連接建立成功
- [x] OCPP 1.6 協定握手完成
- [x] 充電樁ID識別正確

### ✅ 核心訊息測試
- [x] **BootNotification**: 充電樁啟動通知 ✅
- [x] **Heartbeat**: 心跳訊息交換 ✅  
- [x] **StatusNotification**: 狀態通知 ✅

### ⚠️ 遠端控制測試
- [ ] RemoteStartTransaction (待測試)
- [ ] RemoteStopTransaction (待測試)
- [ ] Reset (待測試)

## 🖥️ 可用測試介面

### 1. 網頁客戶端 (http://localhost:3000)
模擬充電樁行為，可以：
- 連接到 OCPP 伺服器
- 發送各種 OCPP 訊息
- 查看實時日誌

### 2. 伺服器管理介面 (server-dashboard.html)
監控伺服器狀態，可以：
- 查看已連接的充電樁
- 發送遠端控制命令
- 監控通訊日誌

## 📋 測試步驟指南

### 第一步：基本連接測試

1. **啟動服務**
   ```bash
   # 啟動 OCPP 伺服器
   npm run server
   
   # 啟動網頁介面
   npm start
   ```

2. **測試網頁客戶端**
   - 打開 http://localhost:3000
   - 使用預設設定（Server URL: ws://*************:8080, ID: 20241024NJ1）
   - 點擊「連線」按鈕
   - 觀察連線狀態和日誌

3. **驗證連接**
   - 狀態應顯示「已連線」
   - 自動發送 BootNotification
   - 每30秒發送 Heartbeat
   - 日誌中顯示訊息交換

### 第二步：功能測試

1. **手動訊息測試**
   - 使用控制面板按鈕發送各種訊息
   - 檢查伺服器回應
   - 確認訊息格式符合 OCPP 規範

2. **自動化測試**
   ```bash
   node test-ocpp.js
   ```
   - 自動執行完整測試序列
   - 生成詳細測試報告
   - 驗證所有基本功能

### 第三步：真實充電樁測試

1. **修改充電樁設定**
   - 登入充電樁管理介面 (http://*************)
   - 將 Server URL 改為 `ws://*************:8080`
   - 保持 Charge Point ID 為 `20241024NJ1`
   - 儲存並重啟充電樁

2. **監控連接**
   - 在伺服器控制台觀察連接狀態
   - 應該看到充電樁成功連接
   - 收到 BootNotification 和定期 Heartbeat

3. **遠端控制測試**
   - 使用管理介面發送遠端命令
   - 測試重啟、啟動/停止充電功能
   - 驗證充電樁正確回應

## 🔧 疑難排解

### 連接問題
- **端口佔用**: 使用 `netstat -an | findstr :8080` 檢查
- **防火牆**: 確保 Windows 防火牆允許端口 8080
- **網路設定**: 確認充電樁和電腦在同一網段

### 訊息交換問題
- **協定版本**: 確認使用 OCPP 1.6J
- **訊息格式**: 檢查 JSON 格式是否正確
- **時間同步**: 確認時間戳格式符合 ISO8601

### 充電樁連接問題
- **URL 格式**: 確認使用 `ws://IP:PORT` 格式
- **充電樁 ID**: 確認 ID 與設定一致
- **網路連通性**: 使用 ping 測試網路連接

## 📊 測試結果總結

| 測試項目 | 狀態 | 備註 |
|---------|------|------|
| WebSocket 連接 | ✅ 通過 | 連接穩定 |
| BootNotification | ✅ 通過 | 自動發送並接收確認 |
| Heartbeat | ✅ 通過 | 30秒間隔正常 |
| StatusNotification | ✅ 通過 | 狀態更新正常 |
| 遠端啟動 | ⚠️ 待測試 | 需實際充電樁驗證 |
| 遠端停止 | ⚠️ 待測試 | 需實際充電樁驗證 |
| 重啟命令 | ⚠️ 待測試 | 需實際充電樁驗證 |

## 🚀 下一步建議

1. **生產環境部署**
   - 設定固定 IP 和端口
   - 添加 SSL/TLS 加密 (WSS)
   - 實作身份驗證機制

2. **功能擴展**
   - 新增更多 OCPP 訊息類型
   - 實作交易管理
   - 新增資料庫儲存

3. **監控和維護**
   - 新增日誌記錄系統
   - 實作錯誤通知
   - 新增效能監控

## 📞 技術支援

如遇到問題，請檢查：
1. 伺服器控制台日誌
2. 瀏覽器開發者工具
3. 充電樁錯誤訊息
4. 網路連接狀態

---

**測試完成時間**: {{ 自動生成 }}  
**測試版本**: OCPP Web Client v1.0.0  
**測試環境**: Windows 10, Node.js 