const WebSocket = require('ws');

class RemoteControlTester {
    constructor() {
        this.ws = null;
        this.messageId = 1;
        this.testResults = [];
    }

    // 產生訊息ID
    generateMessageId() {
        return `remote_test_${Date.now()}_${this.messageId++}`;
    }

    // 記錄日誌
    log(message, type = 'info') {
        const time = new Date().toLocaleTimeString();
        const icons = {
            'info': 'ℹ️',
            'success': '✅',
            'error': '❌',
            'send': '📤',
            'receive': '📥',
            'warning': '⚠️'
        };
        const icon = icons[type] || 'ℹ️';
        console.log(`[${time}] ${icon} ${message}`);
    }

    // 記錄測試結果
    recordTest(testName, success, details = '') {
        this.testResults.push({
            name: testName,
            success: success,
            details: details,
            timestamp: new Date()
        });
        
        const status = success ? '✅ 通過' : '❌ 失敗';
        this.log(`測試 "${testName}": ${status} ${details}`, success ? 'success' : 'error');
    }

    // 連接到充電樁
    async connectToChargePoint(url, chargePointId) {
        return new Promise((resolve, reject) => {
            try {
                this.log(`正在連接到充電樁模擬器: ${url}/${chargePointId}`, 'info');
                
                this.ws = new WebSocket(`${url}/${chargePointId}`, 'ocpp1.6');
                
                this.ws.on('open', () => {
                    this.log('WebSocket 連接已建立', 'success');
                    resolve();
                });

                this.ws.on('message', (data) => {
                    this.handleMessage(data.toString(), chargePointId);
                });

                this.ws.on('close', () => {
                    this.log('WebSocket 連接已關閉', 'warning');
                });

                this.ws.on('error', (error) => {
                    this.log(`WebSocket 錯誤: ${error.message}`, 'error');
                    reject(error);
                });

            } catch (error) {
                this.log(`連接失敗: ${error.message}`, 'error');
                reject(error);
            }
        });
    }

    // 處理收到的訊息
    handleMessage(data, chargePointId) {
        try {
            const message = JSON.parse(data);
            this.log(`從伺服器收到: ${data}`, 'receive');

            const [messageType, messageId, actionOrPayload, payload] = message;

            switch (messageType) {
                case 2: // CALL - 來自伺服器的請求
                    this.handleServerCall(messageId, actionOrPayload, payload, chargePointId);
                    break;
                case 3: // CALLRESULT - 伺服器的回應
                    this.log(`伺服器回應: ${JSON.stringify(actionOrPayload)}`, 'success');
                    break;
                case 4: // CALLERROR - 伺服器的錯誤回應
                    this.log(`伺服器錯誤: ${actionOrPayload}`, 'error');
                    break;
            }
        } catch (error) {
            this.log(`解析訊息失敗: ${error.message}`, 'error');
        }
    }

    // 處理來自伺服器的命令
    handleServerCall(messageId, action, payload, chargePointId) {
        this.log(`收到遠端控制命令: ${action}`, 'info');

        switch (action) {
            case 'RemoteStartTransaction':
                this.recordTest('遠端啟動充電', true, `連接器 ${payload.connectorId}, ID標籤: ${payload.idTag}`);
                
                // 回應啟動成功
                const startResponse = {
                    status: 'Accepted'
                };
                this.sendMessage(3, null, startResponse, messageId);
                
                // 發送狀態變更通知
                setTimeout(() => {
                    this.sendStatusNotification('Preparing', 1);
                    setTimeout(() => {
                        this.sendStatusNotification('Charging', 1);
                        this.startMeterValueSimulation(chargePointId);
                    }, 2000);
                }, 1000);
                break;

            case 'RemoteStopTransaction':
                this.recordTest('遠端停止充電', true, `交易ID: ${payload.transactionId}`);
                
                // 回應停止成功
                const stopResponse = {
                    status: 'Accepted'
                };
                this.sendMessage(3, null, stopResponse, messageId);
                
                // 發送狀態變更通知
                setTimeout(() => {
                    this.sendStatusNotification('Finishing', 1);
                    setTimeout(() => {
                        this.sendStatusNotification('Available', 1);
                        this.stopMeterValueSimulation();
                    }, 2000);
                }, 1000);
                break;

            case 'Reset':
                this.recordTest('遠端重啟', true, `重啟類型: ${payload.type}`);
                
                // 回應重啟成功
                const resetResponse = {
                    status: 'Accepted'
                };
                this.sendMessage(3, null, resetResponse, messageId);
                
                // 模擬重啟過程
                setTimeout(() => {
                    this.sendBootNotification();
                }, 3000);
                break;

            case 'GetConfiguration':
                this.recordTest('讀取設定', true, '設定資料已回傳');
                
                // 回應設定資料
                const configResponse = {
                    configurationKey: [
                        {
                            key: 'MeterValueSampleInterval',
                            value: '60',
                            readonly: false
                        },
                        {
                            key: 'HeartbeatInterval',
                            value: '300',
                            readonly: false
                        }
                    ],
                    unknownKey: []
                };
                this.sendMessage(3, null, configResponse, messageId);
                break;

            default:
                this.log(`未實作的動作: ${action}`, 'warning');
                const errorResponse = [4, messageId, 'NotImplemented', `動作 ${action} 尚未實作`, {}];
                this.ws.send(JSON.stringify(errorResponse));
        }
    }

    // 發送訊息
    sendMessage(messageType, action, payload, originalMessageId = null) {
        const message = originalMessageId 
            ? [messageType, originalMessageId, payload]
            : [messageType, this.generateMessageId(), action, payload];
        
        const messageString = JSON.stringify(message);
        this.log(`發送訊息: ${messageString}`, 'send');
        this.ws.send(messageString);
        return message[1];
    }

    // 發送啟動通知
    sendBootNotification() {
        const payload = {
            chargePointVendor: 'REMOTE_TEST',
            chargePointModel: 'Test-Charger-V1',
            chargePointSerialNumber: 'TEST_REMOTE_001',
            firmwareVersion: '1.0.0-remote-test'
        };

        this.sendMessage(2, 'BootNotification', payload);
        this.log('發送 BootNotification', 'info');
    }

    // 發送心跳
    sendHeartbeat() {
        this.sendMessage(2, 'Heartbeat', {});
        this.log('發送 Heartbeat', 'info');
    }

    // 發送狀態通知
    sendStatusNotification(status, connectorId = 1) {
        const payload = {
            connectorId: connectorId,
            errorCode: 'NoError',
            status: status,
            timestamp: new Date().toISOString()
        };

        this.sendMessage(2, 'StatusNotification', payload);
        this.log(`發送狀態通知: ${status}`, 'info');
    }

    // 發送電錶數值
    sendMeterValues(connectorId = 1, transactionId = null) {
        const currentTime = new Date().toISOString();
        
        // 模擬電錶數據
        const voltage = 220 + (Math.random() - 0.5) * 10; // 215-225V
        const current = Math.random() * 32; // 0-32A
        const power = voltage * current / 1000; // kW
        const energy = Math.random() * 100; // 0-100 kWh
        const temperature = 25 + Math.random() * 15; // 25-40°C

        const payload = {
            connectorId: connectorId,
            meterValue: [
                {
                    timestamp: currentTime,
                    sampledValue: [
                        {
                            value: voltage.toFixed(1),
                            measurand: 'Voltage',
                            unit: 'V'
                        },
                        {
                            value: current.toFixed(2),
                            measurand: 'Current.Import',
                            unit: 'A'
                        },
                        {
                            value: power.toFixed(3),
                            measurand: 'Power.Active.Import',
                            unit: 'kW'
                        },
                        {
                            value: energy.toFixed(2),
                            measurand: 'Energy.Active.Import.Register',
                            unit: 'kWh'
                        },
                        {
                            value: temperature.toFixed(0),
                            measurand: 'Temperature',
                            unit: 'Celsius'
                        }
                    ]
                }
            ]
        };

        if (transactionId) {
            payload.transactionId = transactionId;
        }

        this.sendMessage(2, 'MeterValues', payload);
        this.log(`發送電錶數據: 電壓=${voltage.toFixed(1)}V, 電流=${current.toFixed(2)}A, 功率=${power.toFixed(3)}kW`, 'info');
    }

    // 開始電錶數值模擬
    startMeterValueSimulation(chargePointId, transactionId = Math.floor(Math.random() * 10000)) {
        this.log('開始發送模擬電錶數據', 'info');
        
        this.meterInterval = setInterval(() => {
            this.sendMeterValues(1, transactionId);
        }, 30000); // 每30秒發送一次
    }

    // 停止電錶數值模擬
    stopMeterValueSimulation() {
        if (this.meterInterval) {
            clearInterval(this.meterInterval);
            this.meterInterval = null;
            this.log('停止發送電錶數據', 'info');
        }
    }

    // 執行完整測試
    async runFullTest() {
        try {
            this.log('🚀 開始執行遠端控制完整測試', 'success');
            
            // 連接到伺服器
            await this.connectToChargePoint('ws://localhost:8080', 'REMOTE_TEST_001');
            
            // 等待連接穩定
            await this.delay(1000);
            
            // 發送啟動通知
            this.sendBootNotification();
            await this.delay(2000);
            
            // 發送初始狀態
            this.sendStatusNotification('Available');
            await this.delay(2000);
            
            // 發送心跳
            this.sendHeartbeat();
            await this.delay(2000);
            
            // 發送一次電錶數據
            this.sendMeterValues();
            await this.delay(2000);
            
            this.log('⏳ 等待遠端控制命令...', 'info');
            this.log('💡 請在伺服器終端輸入以下命令進行測試:', 'info');
            this.log('   start  - 測試遠端啟動充電', 'info');
            this.log('   stop   - 測試遠端停止充電', 'info');
            this.log('   reset  - 測試遠端重啟', 'info');
            this.log('   config - 測試讀取設定', 'info');
            
            // 保持連接，等待遠端命令
            this.startHeartbeatInterval();
            
        } catch (error) {
            this.log(`測試失敗: ${error.message}`, 'error');
        }
    }

    // 開始定期心跳
    startHeartbeatInterval() {
        this.heartbeatInterval = setInterval(() => {
            this.sendHeartbeat();
        }, 300000); // 每5分鐘發送一次心跳
    }

    // 延遲函數
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 產生測試報告
    generateReport() {
        this.log('\n📊 遠端控制測試報告', 'info');
        this.log('=' * 50, 'info');
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(test => test.success).length;
        const failedTests = totalTests - passedTests;
        
        this.log(`總測試數: ${totalTests}`, 'info');
        this.log(`通過: ${passedTests}`, 'success');
        this.log(`失敗: ${failedTests}`, failedTests > 0 ? 'error' : 'info');
        
        if (totalTests > 0) {
            this.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 'info');
        }
        
        this.log('\n📋 詳細結果:', 'info');
        this.testResults.forEach((test, index) => {
            const status = test.success ? '✅' : '❌';
            this.log(`${index + 1}. ${status} ${test.name} - ${test.details}`, 'info');
        });
        
        this.log('\n' + '=' * 50, 'info');
    }

    // 關閉連接
    close() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        if (this.meterInterval) {
            clearInterval(this.meterInterval);
        }
        if (this.ws) {
            this.ws.close();
        }
        this.generateReport();
    }
}

// 執行測試
async function main() {
    const tester = new RemoteControlTester();
    
    // 處理程式結束
    process.on('SIGINT', () => {
        console.log('\n正在關閉測試...');
        tester.close();
        process.exit(0);
    });
    
    try {
        await tester.runFullTest();
    } catch (error) {
        console.error('測試執行失敗:', error.message);
        process.exit(1);
    }
}

// 如果直接執行此檔案
if (require.main === module) {
    main();
}

module.exports = RemoteControlTester; 