const http = require('http');
const WebSocket = require('ws');

class NetworkStatusChecker {
    constructor() {
        this.results = [];
    }

    // 記錄日誌
    log(message, type = 'info') {
        const time = new Date().toLocaleTimeString();
        const icons = {
            'info': 'ℹ️',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'test': '🔍'
        };
        const icon = icons[type] || 'ℹ️';
        console.log(`[${time}] ${icon} ${message}`);
    }

    // 檢查端口是否開放
    async checkPort(host, port) {
        return new Promise((resolve) => {
            const socket = new require('net').Socket();
            socket.setTimeout(3000);
            
            socket.on('connect', () => {
                socket.destroy();
                resolve(true);
            });
            
            socket.on('timeout', () => {
                socket.destroy();
                resolve(false);
            });
            
            socket.on('error', () => {
                resolve(false);
            });
            
            socket.connect(port, host);
        });
    }

    // 檢查HTTP API
    async checkHttpAPI() {
        return new Promise((resolve) => {
            const options = {
                hostname: 'localhost',
                port: 8080,
                path: '/api/status',
                method: 'GET',
                timeout: 5000
            };

            const req = http.request(options, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        resolve({ success: true, data: jsonData });
                    } catch (error) {
                        resolve({ success: false, error: error.message });
                    }
                });
            });

            req.on('error', (error) => {
                resolve({ success: false, error: error.message });
            });

            req.on('timeout', () => {
                req.destroy();
                resolve({ success: false, error: 'Timeout' });
            });

            req.end();
        });
    }

    // 測試WebSocket連接
    async testWebSocketConnection() {
        return new Promise((resolve) => {
            try {
                const ws = new WebSocket('ws://localhost:8080/STATUS_CHECK', 'ocpp1.6');
                
                const timeout = setTimeout(() => {
                    ws.close();
                    resolve({ success: false, error: 'Connection timeout' });
                }, 5000);

                ws.on('open', () => {
                    clearTimeout(timeout);
                    ws.close();
                    resolve({ success: true });
                });

                ws.on('error', (error) => {
                    clearTimeout(timeout);
                    resolve({ success: false, error: error.message });
                });

            } catch (error) {
                resolve({ success: false, error: error.message });
            }
        });
    }

    // 執行完整檢查
    async runFullCheck() {
        this.log('🚀 開始網路狀態檢查', 'success');
        this.log('', 'info');

        // 1. 檢查本機端口
        this.log('檢查本機端口狀態...', 'test');
        const portOpen = await this.checkPort('localhost', 8080);
        if (portOpen) {
            this.log('✅ 端口 8080 已開放', 'success');
        } else {
            this.log('❌ 端口 8080 無法連接', 'error');
            return;
        }

        // 2. 檢查HTTP API
        this.log('檢查HTTP API狀態...', 'test');
        const apiResult = await this.checkHttpAPI();
        if (apiResult.success) {
            this.log('✅ HTTP API 正常運作', 'success');
            this.log(`📊 伺服器狀態數據:`, 'info');
            
            if (apiResult.data.connectedChargePoints) {
                this.log(`   • 已連接充電樁數量: ${apiResult.data.connectedChargePoints.length}`, 'info');
                apiResult.data.connectedChargePoints.forEach((cp, index) => {
                    this.log(`   • 充電樁 ${index + 1}: ${cp.id} (${cp.status}) - 最後心跳: ${cp.lastHeartbeat}`, 'info');
                });
            }
            
            if (apiResult.data.serverStatus) {
                this.log(`   • 伺服器狀態: ${apiResult.data.serverStatus.status}`, 'info');
                this.log(`   • 啟動時間: ${apiResult.data.serverStatus.startTime}`, 'info');
            }
        } else {
            this.log(`❌ HTTP API 錯誤: ${apiResult.error}`, 'error');
        }

        // 3. 檢查WebSocket連接
        this.log('檢查WebSocket連接...', 'test');
        const wsResult = await this.testWebSocketConnection();
        if (wsResult.success) {
            this.log('✅ WebSocket 連接正常', 'success');
        } else {
            this.log(`⚠️ WebSocket 連接問題: ${wsResult.error}`, 'warning');
        }

        // 4. 檢查區域網路IP
        this.log('檢查區域網路連接...', 'test');
        const networkPortOpen = await this.checkPort('*************', 8080);
        if (networkPortOpen) {
            this.log('✅ 區域網路端口 *************:8080 可連接', 'success');
        } else {
            this.log('⚠️ 區域網路端口可能無法從外部訪問', 'warning');
        }

        this.log('', 'info');
        this.log('📋 網路檢查完成', 'success');
        this.log('💡 如果充電樁未連接，請確認:', 'info');
        this.log('   1. 充電樁電源是否正常', 'info');
        this.log('   2. 網路連接是否正常 (*************)', 'info');
        this.log('   3. 充電樁OCPP設定是否正確 (ws://*************:8080)', 'info');
        this.log('   4. 防火牆是否阻擋8080端口', 'info');
    }
}

// 執行檢查
async function main() {
    const checker = new NetworkStatusChecker();
    
    try {
        await checker.runFullCheck();
    } catch (error) {
        console.error('檢查執行失敗:', error.message);
    }
}

// 如果直接執行此檔案
if (require.main === module) {
    main();
}

module.exports = NetworkStatusChecker; 