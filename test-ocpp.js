const WebSocket = require('ws');

// 測試配置
const TEST_CONFIG = {
    serverUrl: 'ws://192.168.0.179:8080',
    chargePointId: '20241024NJ1',
    protocol: 'ocpp1.6'
};

// 測試狀態
let testResults = {
    connection: false,
    bootNotification: false,
    heartbeat: false,
    statusNotification: false,
    remoteCommands: false
};

let messageId = 1;
let websocket = null;
let testStartTime = Date.now();

// 生成訊息ID
function generateMessageId() {
    return `test_${Date.now()}_${messageId++}`;
}

// 記錄測試日誌
function testLog(message, type = 'info') {
    const time = new Date().toLocaleTimeString();
    const emoji = {
        'info': 'ℹ️',
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'test': '🧪',
        'send': '📤',
        'receive': '📥'
    };
    
    console.log(`[${time}] ${emoji[type] || 'ℹ️'} ${message}`);
}

// 發送OCPP訊息
function sendMessage(messageType, action, payload) {
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
        testLog('WebSocket未連接，無法發送訊息', 'error');
        return;
    }

    const message = [messageType, generateMessageId(), action, payload];
    const messageString = JSON.stringify(message);
    
    testLog(`發送: ${messageString}`, 'send');
    websocket.send(messageString);
}

// 處理收到的訊息
function handleMessage(data) {
    try {
        const message = JSON.parse(data);
        testLog(`收到: ${data}`, 'receive');

        const [messageType, messageId, actionOrStatus, payload] = message;

        switch (messageType) {
            case 3: // CALLRESULT
                handleCallResult(actionOrStatus, payload);
                break;
            case 4: // CALLERROR
                testLog(`收到錯誤回應: ${actionOrStatus}`, 'error');
                break;
            case 2: // CALL (來自服務器的請求)
                testLog(`收到服務器請求: ${actionOrStatus}`, 'info');
                handleServerCall(messageId, actionOrStatus, payload);
                break;
        }
    } catch (error) {
        testLog(`解析訊息失敗: ${error.message}`, 'error');
    }
}

// 處理服務器的CALL請求
function handleServerCall(messageId, action, payload) {
    testLog(`處理服務器請求: ${action}`, 'test');
    
    // 根據不同的動作回應
    let response = {};
    
    switch (action) {
        case 'Reset':
            response = { status: 'Accepted' };
            testResults.remoteCommands = true;
            testLog('重置命令測試通過', 'success');
            break;
        case 'RemoteStartTransaction':
            response = { status: 'Accepted' };
            testResults.remoteCommands = true;
            testLog('遠端啟動命令測試通過', 'success');
            break;
        case 'RemoteStopTransaction':
            response = { status: 'Accepted' };
            testResults.remoteCommands = true;
            testLog('遠端停止命令測試通過', 'success');
            break;
        default:
            response = { status: 'Rejected' };
    }
    
    // 發送回應
    const callResult = [3, messageId, response];
    websocket.send(JSON.stringify(callResult));
    testLog(`回應服務器: ${JSON.stringify(callResult)}`, 'send');
}

// 處理CALLRESULT回應
function handleCallResult(action, payload) {
    testLog(`${action} 回應成功: ${JSON.stringify(payload)}`, 'success');
    
    // 標記測試結果
    switch (action) {
        case 'BootNotification':
            if (payload.status === 'Accepted') {
                testResults.bootNotification = true;
                testLog('BootNotification 測試通過', 'success');
            }
            break;
        case 'Heartbeat':
            if (payload.currentTime) {
                testResults.heartbeat = true;
                testLog('Heartbeat 測試通過', 'success');
            }
            break;
        case 'StatusNotification':
            testResults.statusNotification = true;
            testLog('StatusNotification 測試通過', 'success');
            break;
    }
}

// 執行測試序列
async function runTestSequence() {
    testLog('開始OCPP測試序列', 'test');
    
    // 等待1秒後發送BootNotification
    setTimeout(() => {
        testLog('發送 BootNotification', 'test');
        sendMessage(2, 'BootNotification', {
            chargePointVendor: "AFAX POWER",
            chargePointModel: "AC-Charger-V1",
            chargePointSerialNumber: TEST_CONFIG.chargePointId,
            firmwareVersion: "1.0.0-test",
            iccid: "",
            imsi: "",
            meterSerialNumber: "METER001",
            meterType: "Energy Meter"
        });
    }, 1000);

    // 3秒後發送Heartbeat
    setTimeout(() => {
        testLog('發送 Heartbeat', 'test');
        sendMessage(2, 'Heartbeat', {});
    }, 3000);

    // 5秒後發送StatusNotification
    setTimeout(() => {
        testLog('發送 StatusNotification', 'test');
        sendMessage(2, 'StatusNotification', {
            connectorId: 1,
            errorCode: "NoError",
            status: "Available",
            timestamp: new Date().toISOString()
        });
    }, 5000);

    // 8秒後顯示測試結果
    setTimeout(() => {
        showTestResults();
    }, 8000);
}

// 顯示測試結果
function showTestResults() {
    const duration = Math.floor((Date.now() - testStartTime) / 1000);
    
    testLog('\n=== OCPP 測試結果摘要 ===', 'test');
    testLog(`測試持續時間: ${duration} 秒`, 'info');
    testLog(`連接狀態: ${testResults.connection ? '✅ 通過' : '❌ 失敗'}`, testResults.connection ? 'success' : 'error');
    testLog(`BootNotification: ${testResults.bootNotification ? '✅ 通過' : '❌ 失敗'}`, testResults.bootNotification ? 'success' : 'warning');
    testLog(`Heartbeat: ${testResults.heartbeat ? '✅ 通過' : '❌ 失敗'}`, testResults.heartbeat ? 'success' : 'warning');
    testLog(`StatusNotification: ${testResults.statusNotification ? '✅ 通過' : '❌ 失敗'}`, testResults.statusNotification ? 'success' : 'warning');
    testLog(`遠端命令: ${testResults.remoteCommands ? '✅ 通過' : '⚠️ 待測試'}`, testResults.remoteCommands ? 'success' : 'warning');
    
    const passedTests = Object.values(testResults).filter(result => result === true).length;
    const totalTests = Object.keys(testResults).length;
    
    testLog(`\n總體結果: ${passedTests}/${totalTests} 項測試通過`, passedTests === totalTests ? 'success' : 'warning');
    
    if (passedTests === totalTests) {
        testLog('🎉 所有測試通過！OCPP系統運行正常', 'success');
    } else {
        testLog('⚠️ 部分測試未通過，請檢查系統設定', 'warning');
    }
    
    testLog('\n測試完成。按 Ctrl+C 結束程式', 'info');
}

// 開始測試
function startTest() {
    testLog('🚀 啟動 OCPP 自動化測試', 'test');
    testLog(`測試目標: ${TEST_CONFIG.serverUrl}/${TEST_CONFIG.chargePointId}`, 'info');
    
    // 建立WebSocket連接
    const fullUrl = `${TEST_CONFIG.serverUrl}/${TEST_CONFIG.chargePointId}`;
    websocket = new WebSocket(fullUrl, TEST_CONFIG.protocol);

    // WebSocket事件處理
    websocket.on('open', () => {
        testLog('✅ WebSocket 連接成功', 'success');
        testResults.connection = true;
        runTestSequence();
    });

    websocket.on('message', (data) => {
        handleMessage(data.toString());
    });

    websocket.on('error', (error) => {
        testLog(`❌ WebSocket 錯誤: ${error.message}`, 'error');
        testResults.connection = false;
    });

    websocket.on('close', (code, reason) => {
        testLog(`連接已關閉. Code: ${code}, Reason: ${reason || '無'}`, 'warning');
        testResults.connection = false;
    });
}

// 優雅退出
process.on('SIGINT', () => {
    testLog('\n正在結束測試...', 'info');
    if (websocket) {
        websocket.close();
    }
    process.exit(0);
});

// 啟動測試
testLog('OCPP 測試工具已準備就緒', 'info');
testLog('按任意鍵開始測試，或 Ctrl+C 退出', 'info');

// 等待用戶輸入或自動開始
setTimeout(() => {
    startTest();
}, 2000); 