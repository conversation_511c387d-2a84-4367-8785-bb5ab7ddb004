const WebSocket = require('ws');

console.log('🔧 簡化版 OCPP 連接測試工具');
console.log('==================================================');

// 測試配置
const configs = [
    // 不同的URL格式測試
    { url: 'ws://192.168.0.179:8080/20241024NJ1', protocol: 'ocpp1.6', name: '標準格式' },
    { url: 'ws://192.168.0.179:8080', protocol: 'ocpp1.6', name: '無ID格式' },
    { url: 'ws://192.168.0.179:8080/ocpp/20241024NJ1', protocol: 'ocpp1.6', name: 'ocpp路徑格式' },
    
    // 不同的協定版本測試
    { url: 'ws://192.168.0.179:8080/20241024NJ1', protocol: 'ocpp2.0', name: 'OCPP 2.0' },
    { url: 'ws://192.168.0.179:8080/20241024NJ1', protocol: 'ocpp2.0.1', name: 'OCPP 2.0.1' },
    
    // 不同的端口測試
    { url: 'ws://192.168.0.179:8081/20241024NJ1', protocol: 'ocpp1.6', name: '端口8081' },
    { url: 'ws://192.168.0.179:9000/20241024NJ1', protocol: 'ocpp1.6', name: '端口9000' },
];

let testIndex = 0;
let successfulConnections = [];
let failedConnections = [];

function log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = {
        'info': 'ℹ️',
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'test': '🧪'
    }[type] || 'ℹ️';
    
    console.log(`[${timestamp}] ${prefix} ${message}`);
}

function testConnection(config) {
    return new Promise((resolve) => {
        log(`測試 ${testIndex + 1}/${configs.length}: ${config.name}`, 'test');
        log(`URL: ${config.url}`, 'info');
        log(`協定: ${config.protocol}`, 'info');
        
        const startTime = Date.now();
        let ws;
        
        try {
            // 設定連接超時
            const timeout = setTimeout(() => {
                log(`連接超時 (5秒)`, 'error');
                if (ws) {
                    ws.terminate();
                }
                failedConnections.push({
                    ...config,
                    error: '連接超時',
                    duration: Date.now() - startTime
                });
                resolve();
            }, 5000);
            
            // 建立WebSocket連接
            ws = new WebSocket(config.url, config.protocol);
            
            ws.on('open', () => {
                const duration = Date.now() - startTime;
                clearTimeout(timeout);
                log(`連接成功! 耗時: ${duration}ms`, 'success');
                log(`協定: ${ws.protocol}`, 'info');
                log(`就緒狀態: ${ws.readyState}`, 'info');
                
                successfulConnections.push({
                    ...config,
                    duration: duration,
                    protocol: ws.protocol,
                    readyState: ws.readyState
                });
                
                // 嘗試發送簡單的OCPP訊息
                const bootMessage = JSON.stringify([
                    2,
                    `test_${Date.now()}`,
                    'BootNotification',
                    {
                        chargePointVendor: 'SimpleTest',
                        chargePointModel: 'TestModel',
                        chargePointSerialNumber: 'SIMPLE_001',
                        firmwareVersion: '1.0.0'
                    }
                ]);
                
                log(`發送BootNotification測試訊息`, 'info');
                ws.send(bootMessage);
                
                // 等待2秒後關閉連接
                setTimeout(() => {
                    ws.close();
                }, 2000);
            });
            
            ws.on('message', (data) => {
                log(`收到回應: ${data}`, 'success');
                try {
                    const message = JSON.parse(data);
                    if (message[0] === 3) { // CALLRESULT
                        log(`BootNotification回應: ${JSON.stringify(message[2])}`, 'success');
                    }
                } catch (e) {
                    log(`解析回應失敗: ${e.message}`, 'warning');
                }
            });
            
            ws.on('close', (code, reason) => {
                clearTimeout(timeout);
                log(`連接關閉 - 代碼: ${code}, 原因: ${reason || '無'}`, 'info');
                resolve();
            });
            
            ws.on('error', (error) => {
                clearTimeout(timeout);
                log(`連接錯誤: ${error.message}`, 'error');
                
                // 分析錯誤類型
                if (error.code === 'ECONNREFUSED') {
                    log(`💡 連接被拒絕 - 伺服器可能未運行`, 'warning');
                } else if (error.code === 'ENOTFOUND') {
                    log(`💡 主機未找到 - 檢查IP地址`, 'warning');
                } else if (error.code === 'ETIMEDOUT') {
                    log(`💡 連接超時 - 檢查網路或防火牆`, 'warning');
                }
                
                failedConnections.push({
                    ...config,
                    error: error.message,
                    errorCode: error.code,
                    duration: Date.now() - startTime
                });
                resolve();
            });
            
        } catch (error) {
            log(`建立連接時發生錯誤: ${error.message}`, 'error');
            failedConnections.push({
                ...config,
                error: error.message,
                duration: Date.now() - startTime
            });
            resolve();
        }
        
        console.log(''); // 空行分隔
    });
}

async function runAllTests() {
    log('開始執行連接測試...', 'test');
    console.log('');
    
    for (const config of configs) {
        await testConnection(config);
        testIndex++;
        
        // 測試間隔
        if (testIndex < configs.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    // 顯示測試結果摘要
    console.log('');
    console.log('📊 測試結果摘要');
    console.log('==================================================');
    
    log(`總測試數: ${configs.length}`, 'info');
    log(`成功連接: ${successfulConnections.length}`, 'success');
    log(`失敗連接: ${failedConnections.length}`, 'error');
    
    if (successfulConnections.length > 0) {
        console.log('');
        console.log('✅ 成功的連接:');
        successfulConnections.forEach((conn, index) => {
            console.log(`  ${index + 1}. ${conn.name}`);
            console.log(`     URL: ${conn.url}`);
            console.log(`     協定: ${conn.protocol}`);
            console.log(`     耗時: ${conn.duration}ms`);
            console.log('');
        });
    }
    
    if (failedConnections.length > 0) {
        console.log('❌ 失敗的連接:');
        failedConnections.forEach((conn, index) => {
            console.log(`  ${index + 1}. ${conn.name}`);
            console.log(`     URL: ${conn.url}`);
            console.log(`     錯誤: ${conn.error}`);
            if (conn.errorCode) {
                console.log(`     錯誤代碼: ${conn.errorCode}`);
            }
            console.log('');
        });
    }
    
    // 提供建議
    console.log('💡 建議:');
    if (successfulConnections.length > 0) {
        const bestConnection = successfulConnections[0];
        console.log(`• 建議使用: ${bestConnection.url} (協定: ${bestConnection.protocol})`);
        console.log(`• 此配置連接成功且穩定`);
    } else {
        console.log('• 所有連接都失敗，請檢查:');
        console.log('  - OCPP伺服器是否正在運行');
        console.log('  - 網路連接是否正常');
        console.log('  - 防火牆設定是否正確');
        console.log('  - IP地址和端口是否正確');
    }
    
    console.log('');
    log('測試完成!', 'success');
}

// 執行測試
runAllTests().catch(error => {
    log(`測試執行失敗: ${error.message}`, 'error');
}); 