const net = require('net');
const { execSync } = require('child_process');

console.log('🌐 網路連接診斷工具');
console.log('==================================================');

// 測試配置
const TARGET_IP = '*************';
const TEST_PORTS = [8080, 8081, 9000, 80, 443];

function log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = {
        'info': 'ℹ️',
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'test': '🧪'
    }[type] || 'ℹ️';
    
    console.log(`[${timestamp}] ${prefix} ${message}`);
}

// 1. Ping 測試
function testPing() {
    return new Promise((resolve) => {
        log('執行 Ping 測試...', 'test');
        
        try {
            const command = process.platform === 'win32' 
                ? `ping -n 4 ${TARGET_IP}`
                : `ping -c 4 ${TARGET_IP}`;
                
            const result = execSync(command, { encoding: 'utf8', timeout: 10000 });
            
            if (result.includes('TTL=') || result.includes('ttl=')) {
                log(`Ping 成功 - ${TARGET_IP} 可達`, 'success');
                resolve(true);
            } else {
                log(`Ping 失敗 - ${TARGET_IP} 不可達`, 'error');
                resolve(false);
            }
        } catch (error) {
            log(`Ping 測試失敗: ${error.message}`, 'error');
            resolve(false);
        }
    });
}

// 2. TCP 端口測試
function testTcpPort(port) {
    return new Promise((resolve) => {
        const socket = new net.Socket();
        const timeout = 5000;
        
        log(`測試 TCP 端口 ${port}...`, 'test');
        
        const timer = setTimeout(() => {
            socket.destroy();
            log(`端口 ${port} 連接超時`, 'error');
            resolve({ port, success: false, error: '連接超時' });
        }, timeout);
        
        socket.connect(port, TARGET_IP, () => {
            clearTimeout(timer);
            socket.destroy();
            log(`端口 ${port} 連接成功`, 'success');
            resolve({ port, success: true });
        });
        
        socket.on('error', (error) => {
            clearTimeout(timer);
            log(`端口 ${port} 連接失敗: ${error.code}`, 'error');
            resolve({ port, success: false, error: error.code });
        });
    });
}

// 3. 路由追蹤 (簡化版)
function testRoute() {
    return new Promise((resolve) => {
        log('執行路由測試...', 'test');
        
        try {
            const command = process.platform === 'win32'
                ? `tracert -h 10 ${TARGET_IP}`
                : `traceroute -m 10 ${TARGET_IP}`;
                
            const result = execSync(command, { encoding: 'utf8', timeout: 15000 });
            log('路由追蹤完成', 'success');
            console.log(result);
            resolve(true);
        } catch (error) {
            log(`路由追蹤失敗: ${error.message}`, 'error');
            resolve(false);
        }
    });
}

// 4. 檢查本機網路配置
function checkNetworkConfig() {
    log('檢查本機網路配置...', 'test');
    
    try {
        const command = process.platform === 'win32' ? 'ipconfig' : 'ifconfig';
        const result = execSync(command, { encoding: 'utf8' });
        
        // 提取IP地址
        const ipRegex = /192\.168\.0\.\d+/g;
        const ips = result.match(ipRegex);
        
        if (ips && ips.length > 0) {
            log(`本機IP地址: ${ips.join(', ')}`, 'success');
            
            // 檢查是否在同一網段
            const targetNetwork = TARGET_IP.substring(0, TARGET_IP.lastIndexOf('.'));
            const localNetwork = ips[0].substring(0, ips[0].lastIndexOf('.'));
            
            if (targetNetwork === localNetwork) {
                log('✅ 本機與目標在同一網段', 'success');
            } else {
                log('⚠️ 本機與目標不在同一網段', 'warning');
            }
        } else {
            log('未找到本機IP地址', 'warning');
        }
    } catch (error) {
        log(`檢查網路配置失敗: ${error.message}`, 'error');
    }
}

// 5. WebSocket 連接測試 (原始TCP)
function testRawWebSocket(port) {
    return new Promise((resolve) => {
        log(`測試原始 WebSocket 握手 (端口 ${port})...`, 'test');
        
        const socket = new net.Socket();
        const timeout = 5000;
        
        const timer = setTimeout(() => {
            socket.destroy();
            log(`WebSocket 握手超時 (端口 ${port})`, 'error');
            resolve({ port, success: false, error: '握手超時' });
        }, timeout);
        
        socket.connect(port, TARGET_IP, () => {
            // 發送 WebSocket 握手請求
            const handshake = [
                'GET /20241024NJ1 HTTP/1.1',
                `Host: ${TARGET_IP}:${port}`,
                'Upgrade: websocket',
                'Connection: Upgrade',
                'Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==',
                'Sec-WebSocket-Protocol: ocpp1.6',
                'Sec-WebSocket-Version: 13',
                '',
                ''
            ].join('\r\n');
            
            socket.write(handshake);
        });
        
        socket.on('data', (data) => {
            clearTimeout(timer);
            const response = data.toString();
            log(`WebSocket 握手回應 (端口 ${port}):`, 'info');
            console.log(response);
            
            if (response.includes('101 Switching Protocols')) {
                log(`WebSocket 握手成功 (端口 ${port})`, 'success');
                resolve({ port, success: true, response });
            } else {
                log(`WebSocket 握手失敗 (端口 ${port})`, 'error');
                resolve({ port, success: false, response });
            }
            socket.destroy();
        });
        
        socket.on('error', (error) => {
            clearTimeout(timer);
            log(`WebSocket 測試失敗 (端口 ${port}): ${error.code}`, 'error');
            resolve({ port, success: false, error: error.code });
        });
    });
}

// 主測試函數
async function runNetworkTests() {
    console.log(`🎯 目標主機: ${TARGET_IP}`);
    console.log(`🔍 測試端口: ${TEST_PORTS.join(', ')}`);
    console.log('');
    
    // 1. 檢查本機網路配置
    checkNetworkConfig();
    console.log('');
    
    // 2. Ping 測試
    const pingResult = await testPing();
    console.log('');
    
    if (!pingResult) {
        log('⚠️ Ping 失敗，可能存在網路連接問題', 'warning');
        log('建議檢查:', 'info');
        log('• 目標主機是否開機', 'info');
        log('• 網路線是否連接正常', 'info');
        log('• 防火牆是否阻擋 ICMP', 'info');
        console.log('');
    }
    
    // 3. TCP 端口測試
    log('開始 TCP 端口掃描...', 'test');
    const portResults = [];
    
    for (const port of TEST_PORTS) {
        const result = await testTcpPort(port);
        portResults.push(result);
        await new Promise(resolve => setTimeout(resolve, 500)); // 間隔500ms
    }
    
    console.log('');
    
    // 4. WebSocket 握手測試 (針對開放的端口)
    const openPorts = portResults.filter(r => r.success).map(r => r.port);
    
    if (openPorts.length > 0) {
        log('開始 WebSocket 握手測試...', 'test');
        
        for (const port of openPorts) {
            const wsResult = await testRawWebSocket(port);
            await new Promise(resolve => setTimeout(resolve, 1000)); // 間隔1秒
        }
    }
    
    console.log('');
    
    // 5. 顯示測試摘要
    console.log('📊 測試結果摘要');
    console.log('==================================================');
    
    log(`Ping 測試: ${pingResult ? '✅ 成功' : '❌ 失敗'}`, 'info');
    
    const openPortCount = portResults.filter(r => r.success).length;
    log(`開放端口: ${openPortCount}/${TEST_PORTS.length}`, 'info');
    
    if (openPortCount > 0) {
        console.log('');
        console.log('✅ 開放的端口:');
        portResults.filter(r => r.success).forEach(r => {
            console.log(`  • 端口 ${r.port}: 可連接`);
        });
    }
    
    if (openPortCount < TEST_PORTS.length) {
        console.log('');
        console.log('❌ 關閉的端口:');
        portResults.filter(r => !r.success).forEach(r => {
            console.log(`  • 端口 ${r.port}: ${r.error || '無法連接'}`);
        });
    }
    
    console.log('');
    
    // 6. 提供建議
    console.log('💡 建議:');
    if (openPortCount === 0) {
        console.log('• 所有測試端口都無法連接');
        console.log('• 請確認 OCPP 伺服器是否正在運行');
        console.log('• 檢查防火牆設定');
        console.log('• 確認目標IP地址正確');
    } else {
        const recommendedPort = openPorts.includes(8080) ? 8080 : openPorts[0];
        console.log(`• 建議使用端口 ${recommendedPort} 進行 OCPP 連接`);
        console.log(`• 充電樁 Server URL 設定為: ws://${TARGET_IP}:${recommendedPort}/20241024NJ1`);
    }
    
    console.log('');
    log('網路診斷完成!', 'success');
}

// 執行測試
runNetworkTests().catch(error => {
    log(`網路測試失敗: ${error.message}`, 'error');
}); 