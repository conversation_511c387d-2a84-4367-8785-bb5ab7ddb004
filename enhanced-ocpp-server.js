const WebSocket = require('ws');
const http = require('http');
const url = require('url');

// 建立HTTP伺服器
const server = http.createServer((req, res) => {
    // 設置CORS頭部
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    const parsedUrl = url.parse(req.url, true);
    
    // API端點處理
    if (req.method === 'POST' && parsedUrl.pathname === '/api/remote-control') {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                const result = handleRemoteControlAPI(data);
                
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(result));
            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ success: false, error: error.message }));
            }
        });
    }
    // 獲取充電樁狀態API
    else if (req.method === 'GET' && parsedUrl.pathname === '/api/status') {
        const status = getChargePointsStatus();
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(status));
    }
    else {
        res.writeHead(404);
        res.end('Not Found');
    }
});

// 建立WebSocket伺服器
const wss = new WebSocket.Server({
    server,
    verifyClient: (info) => {
        const protocols = info.req.headers['sec-websocket-protocol'];
        const pathname = url.parse(info.req.url).pathname;
        const chargePointId = pathname.substring(1);
        
        // 只接受真實充電樁連接，過濾測試連接
        if (chargePointId.includes('TEST') || chargePointId.includes('test')) {
            console.log(`❌ 拒絕測試連接: ${chargePointId}`);
            return false;
        }
        
        if (protocols && protocols.includes('ocpp1.6')) {
            return true;
        }
        console.log('❌ 連接被拒絕：缺少 ocpp1.6 協定');
        return false;
    }
});

// 儲存已連接的充電樁
const connectedChargePoints = new Map();
let messageId = 1;

// 產生唯一訊息ID
function generateMessageId() {
    return `central_${Date.now()}_${messageId++}`;
}

// 記錄日誌
function log(message, type = 'info') {
    const time = new Date().toLocaleTimeString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'send' ? '📤' : type === 'receive' ? '📥' : 'ℹ️';
    console.log(`[${time}] ${prefix} ${message}`);
}

// 發送OCPP訊息
function sendMessage(ws, messageType, action, payload, originalMessageId = null) {
    const message = originalMessageId 
        ? [messageType, originalMessageId, payload]
        : [messageType, generateMessageId(), action, payload];
    
    const messageString = JSON.stringify(message);
    log(`發送給充電樁: ${messageString}`, 'send');
    ws.send(messageString);
    return message[1]; // 返回訊息ID用於追蹤
}

// 處理收到的OCPP訊息
function handleMessage(ws, chargePointId, data) {
    try {
        const message = JSON.parse(data);
        log(`從 ${chargePointId} 收到: ${data}`, 'receive');

        const [messageType, messageId, actionOrStatus, payload] = message;

        switch (messageType) {
            case 2: // CALL - 來自充電樁的請求
                handleChargePointCall(ws, chargePointId, messageId, actionOrStatus, payload);
                break;
            case 3: // CALLRESULT - 充電樁的回應
                log(`${chargePointId} 回應成功: ${JSON.stringify(payload)}`, 'success');
                break;
            case 4: // CALLERROR - 充電樁的錯誤回應
                log(`${chargePointId} 回應錯誤: ${actionOrStatus}`, 'error');
                break;
        }
    } catch (error) {
        log(`解析訊息失敗: ${error.message}`, 'error');
    }
}

// 處理來自充電樁的CALL訊息
function handleChargePointCall(ws, chargePointId, messageId, action, payload) {
    log(`處理 ${chargePointId} 的 ${action} 請求`, 'info');

    switch (action) {
        case 'BootNotification':
            const bootResponse = {
                currentTime: new Date().toISOString(),
                interval: 300,
                status: 'Accepted'
            };
            sendMessage(ws, 3, null, bootResponse, messageId);
            log(`${chargePointId} 啟動通知已接受`, 'success');
            
            // 啟動後立即請求電錶數據
            setTimeout(() => {
                requestMeterValues(chargePointId);
            }, 5000);
            break;

        case 'Heartbeat':
            const heartbeatResponse = {
                currentTime: new Date().toISOString()
            };
            sendMessage(ws, 3, null, heartbeatResponse, messageId);
            log(`${chargePointId} 心跳回應`, 'success');
            updateChargePointHeartbeat(chargePointId);
            
            // 每次心跳時請求電錶數據（如果是真實充電樁）
            if (!chargePointId.includes('TEST') && !chargePointId.includes('test')) {
                requestMeterValues(chargePointId);
            }
            break;

        case 'StatusNotification':
            sendMessage(ws, 3, null, {}, messageId);
            log(`${chargePointId} 狀態: ${payload.status}, 連接器: ${payload.connectorId}`, 'info');
            updateChargePointStatus(chargePointId, payload);
            
            // 狀態更新時也請求電錶數據
            if (!chargePointId.includes('TEST') && !chargePointId.includes('test')) {
                setTimeout(() => {
                    requestMeterValues(chargePointId);
                }, 2000);
            }
            break;

        case 'StartTransaction':
            const startResponse = {
                transactionId: Math.floor(Math.random() * 10000),
                idTagInfo: {
                    status: 'Accepted'
                }
            };
            sendMessage(ws, 3, null, startResponse, messageId);
            log(`${chargePointId} 交易已啟動，ID: ${startResponse.transactionId}`, 'success');
            
            // 交易開始時請求電錶數據
            setTimeout(() => {
                requestMeterValues(chargePointId);
            }, 1000);
            break;

        case 'StopTransaction':
            const stopResponse = {
                idTagInfo: {
                    status: 'Accepted'
                }
            };
            sendMessage(ws, 3, null, stopResponse, messageId);
            log(`${chargePointId} 交易已停止，ID: ${payload.transactionId}`, 'success');
            break;

        case 'MeterValues':
            sendMessage(ws, 3, null, {}, messageId);
            processMeterValues(chargePointId, payload);
            log(`${chargePointId} 電錶數據已接收`, 'success');
            break;

        case 'DataTransfer':
            sendMessage(ws, 3, null, { status: 'Accepted' }, messageId);
            log(`${chargePointId} 數據傳輸: ${payload.data}`, 'info');
            break;

        default:
            const errorResponse = [4, messageId, 'NotImplemented', `動作 ${action} 尚未實作`, {}];
            ws.send(JSON.stringify(errorResponse));
            log(`未實作的動作: ${action}`, 'error');
    }
}

// 處理電錶數值
function processMeterValues(chargePointId, payload) {
    const values = payload.meterValue || [];
    
    values.forEach(meterValue => {
        const timestamp = meterValue.timestamp;
        const sampledValues = meterValue.sampledValue || [];
        
        sampledValues.forEach(sample => {
            const measurand = sample.measurand || 'Energy.Active.Import.Register';
            const value = parseFloat(sample.value);
            const unit = sample.unit || 'Wh';
            
            log(`${chargePointId} 電錶數據: ${measurand} = ${value} ${unit}`, 'info');
            
            // 儲存或處理電錶數據
            updateMeterData(chargePointId, measurand, value, unit, timestamp);
        });
    });
}

// 更新充電樁狀態
function updateChargePointStatus(chargePointId, statusData) {
    const chargePoint = connectedChargePoints.get(chargePointId);
    if (chargePoint) {
        chargePoint.status = statusData.status;
        chargePoint.connectorId = statusData.connectorId;
        chargePoint.errorCode = statusData.errorCode;
        chargePoint.lastStatusUpdate = new Date();
    }
}

// 更新心跳時間
function updateChargePointHeartbeat(chargePointId) {
    const chargePoint = connectedChargePoints.get(chargePointId);
    if (chargePoint) {
        chargePoint.lastHeartbeat = new Date();
    }
}

// 更新電錶數據
function updateMeterData(chargePointId, measurand, value, unit, timestamp) {
    const chargePoint = connectedChargePoints.get(chargePointId);
    if (chargePoint) {
        if (!chargePoint.meterData) {
            chargePoint.meterData = {};
        }
        
        chargePoint.meterData[measurand] = {
            value: value,
            unit: unit,
            timestamp: timestamp,
            updated: new Date()
        };
    }
}

// 遠端控制功能
function sendRemoteStartTransaction(chargePointId, connectorId = 1, idTag = 'REMOTE_START') {
    const chargePoint = connectedChargePoints.get(chargePointId);
    if (!chargePoint) {
        log(`充電樁 ${chargePointId} 未連接`, 'error');
        return false;
    }

    const payload = {
        connectorId: connectorId,
        idTag: idTag
    };

    const msgId = sendMessage(chargePoint.ws, 2, 'RemoteStartTransaction', payload);
    log(`發送遠端啟動充電命令到 ${chargePointId}`, 'info');
    return msgId;
}

function sendRemoteStopTransaction(chargePointId, transactionId) {
    const chargePoint = connectedChargePoints.get(chargePointId);
    if (!chargePoint) {
        log(`充電樁 ${chargePointId} 未連接`, 'error');
        return false;
    }

    const payload = {
        transactionId: transactionId
    };

    const msgId = sendMessage(chargePoint.ws, 2, 'RemoteStopTransaction', payload);
    log(`發送遠端停止充電命令到 ${chargePointId}`, 'info');
    return msgId;
}

function sendReset(chargePointId, type = 'Soft') {
    const chargePoint = connectedChargePoints.get(chargePointId);
    if (!chargePoint) {
        log(`充電樁 ${chargePointId} 未連接`, 'error');
        return false;
    }

    const payload = { type: type };
    const msgId = sendMessage(chargePoint.ws, 2, 'Reset', payload);
    log(`發送重啟命令到 ${chargePointId} (${type})`, 'info');
    return msgId;
}

function sendGetConfiguration(chargePointId, key = []) {
    const chargePoint = connectedChargePoints.get(chargePointId);
    if (!chargePoint) {
        log(`充電樁 ${chargePointId} 未連接`, 'error');
        return false;
    }

    const payload = key.length > 0 ? { key: key } : {};
    const msgId = sendMessage(chargePoint.ws, 2, 'GetConfiguration', payload);
    log(`發送讀取設定命令到 ${chargePointId}`, 'info');
    return msgId;
}

function requestMeterValues(chargePointId, connectorId = 1) {
    const chargePoint = connectedChargePoints.get(chargePointId);
    if (!chargePoint) {
        log(`充電樁 ${chargePointId} 未連接`, 'error');
        return false;
    }

    const payload = {
        connectorId: connectorId
    };

    const msgId = sendMessage(chargePoint.ws, 2, 'TriggerMessage', {
        requestedMessage: 'MeterValues',
        connectorId: connectorId
    });
    log(`請求 ${chargePointId} 發送電錶數據`, 'info');
    return msgId;
}

// WebSocket連接處理
wss.on('connection', (ws, request) => {
    const pathname = url.parse(request.url).pathname;
    const chargePointId = pathname.substring(1);

    if (!chargePointId) {
        log('連接被拒絕：缺少充電樁ID', 'error');
        ws.close();
        return;
    }

    log(`✅ 充電樁 ${chargePointId} 已連接`, 'success');
    log(`   來源IP: ${request.socket.remoteAddress}`, 'info');
    log(`   連接協定: ${ws.protocol}`, 'info');

    // 儲存連接
    connectedChargePoints.set(chargePointId, {
        ws: ws,
        connectedAt: new Date(),
        lastHeartbeat: new Date(),
        status: 'Available',
        connectorId: 1,
        errorCode: 'NoError',
        meterData: {}
    });

    if (request.headers['sec-websocket-protocol']) {
        ws.protocol = 'ocpp1.6';
    }

    ws.on('message', (data) => {
        handleMessage(ws, chargePointId, data.toString());
    });

    ws.on('close', () => {
        log(`❌ 充電樁 ${chargePointId} 已斷線`, 'error');
        connectedChargePoints.delete(chargePointId);
    });

    ws.on('error', (error) => {
        log(`充電樁 ${chargePointId} 發生錯誤: ${error.message}`, 'error');
    });
});

// 定期顯示連接狀態和數據
setInterval(() => {
    if (connectedChargePoints.size > 0) {
        log(`\n📊 已連接的真實充電樁: ${connectedChargePoints.size}`, 'info');
        connectedChargePoints.forEach((info, id) => {
            // 只顯示真實充電樁
            if (!id.includes('TEST') && !id.includes('test')) {
                const duration = Math.floor((Date.now() - info.connectedAt) / 1000);
                const heartbeatAge = Math.floor((Date.now() - info.lastHeartbeat) / 1000);
                log(`   • ${id} (連接 ${duration} 秒, 最後心跳 ${heartbeatAge} 秒前)`, 'info');
                log(`     狀態: ${info.status}, 連接器: ${info.connectorId}, 錯誤: ${info.errorCode}`, 'info');
                
                // 顯示電錶數據
                if (info.meterData && Object.keys(info.meterData).length > 0) {
                    Object.entries(info.meterData).forEach(([measurand, data]) => {
                        const updateAge = Math.floor((Date.now() - data.updated) / 1000);
                        log(`     📊 ${measurand}: ${data.value} ${data.unit} (${updateAge}秒前)`, 'info');
                    });
                } else {
                    log(`     📊 等待電錶數據...`, 'info');
                }
            }
        });
        log('', 'info');
    }
}, 30000);

// 更頻繁地請求真實充電樁的電錶數據
setInterval(() => {
    connectedChargePoints.forEach((info, chargePointId) => {
        // 只請求真實充電樁的數據
        if (!chargePointId.includes('TEST') && !chargePointId.includes('test')) {
            requestMeterValues(chargePointId);
            log(`📊 定期請求 ${chargePointId} 電錶數據`, 'info');
        }
    });
}, 30000); // 每30秒請求一次

// 處理遠端控制API請求
function handleRemoteControlAPI(data) {
    const { action, chargePointId, params } = data;
    
    if (connectedChargePoints.size === 0) {
        return { success: false, message: '沒有連接的充電樁' };
    }
    
    // 如果沒有指定充電樁ID，使用第一個真實充電樁
    let targetChargePointId = chargePointId;
    if (!targetChargePointId) {
        for (const [id] of connectedChargePoints) {
            if (!id.includes('TEST') && !id.includes('test')) {
                targetChargePointId = id;
                break;
            }
        }
    }
    
    if (!targetChargePointId) {
        return { success: false, message: '沒有找到真實充電樁' };
    }
    
    let result = { success: false, message: '未知命令' };
    
    switch (action) {
        case 'start':
            const startResult = sendRemoteStartTransaction(targetChargePointId, params?.connectorId, params?.idTag);
            result = { 
                success: !!startResult, 
                message: startResult ? `遠端啟動充電命令已發送到 ${targetChargePointId}` : '發送失敗',
                messageId: startResult
            };
            break;
            
        case 'stop':
            const stopResult = sendRemoteStopTransaction(targetChargePointId, params?.transactionId);
            result = { 
                success: !!stopResult, 
                message: stopResult ? `遠端停止充電命令已發送到 ${targetChargePointId}` : '發送失敗',
                messageId: stopResult
            };
            break;
            
        case 'reset':
            const resetResult = sendReset(targetChargePointId, params?.type);
            result = { 
                success: !!resetResult, 
                message: resetResult ? `重啟命令已發送到 ${targetChargePointId}` : '發送失敗',
                messageId: resetResult
            };
            break;
            
        case 'config':
            const configResult = sendGetConfiguration(targetChargePointId, params?.key);
            result = { 
                success: !!configResult, 
                message: configResult ? `讀取設定命令已發送到 ${targetChargePointId}` : '發送失敗',
                messageId: configResult
            };
            break;
            
        case 'meter':
            const meterResult = requestMeterValues(targetChargePointId, params?.connectorId);
            result = { 
                success: !!meterResult, 
                message: meterResult ? `電錶數據請求已發送到 ${targetChargePointId}` : '發送失敗',
                messageId: meterResult
            };
            break;
    }
    
    return result;
}

// 獲取充電樁狀態
function getChargePointsStatus() {
    const status = {
        totalConnected: connectedChargePoints.size,
        chargePoints: []
    };
    
    connectedChargePoints.forEach((info, id) => {
        // 只返回真實充電樁的狀態
        if (!id.includes('TEST') && !id.includes('test')) {
            const duration = Math.floor((Date.now() - info.connectedAt) / 1000);
            const heartbeatAge = Math.floor((Date.now() - info.lastHeartbeat) / 1000);
            
            status.chargePoints.push({
                id: id,
                status: info.status,
                connectorId: info.connectorId,
                errorCode: info.errorCode,
                connectedDuration: duration,
                lastHeartbeatAge: heartbeatAge,
                meterData: info.meterData || {}
            });
        }
    });
    
    return status;
}

// 命令行介面 - 用於測試遠端控制
process.stdin.setEncoding('utf8');
process.stdin.on('readable', () => {
    let chunk;
    while ((chunk = process.stdin.read()) !== null) {
        const command = chunk.trim().toLowerCase();
        const parts = command.split(' ');
        
        if (connectedChargePoints.size === 0) {
            console.log('沒有連接的充電樁');
            continue;
        }
        
        // 使用第一個真實充電樁
        let chargePointId = null;
        for (const [id] of connectedChargePoints) {
            if (!id.includes('TEST') && !id.includes('test')) {
                chargePointId = id;
                break;
            }
        }
        
        if (!chargePointId) {
            console.log('沒有找到真實充電樁');
            continue;
        }
        
        switch (parts[0]) {
            case 'start':
                sendRemoteStartTransaction(chargePointId);
                break;
            case 'stop':
                const transactionId = parts[1] || '1';
                sendRemoteStopTransaction(chargePointId, transactionId);
                break;
            case 'reset':
                sendReset(chargePointId);
                break;
            case 'config':
                sendGetConfiguration(chargePointId);
                break;
            case 'meter':
                requestMeterValues(chargePointId);
                break;
            case 'help':
                console.log('可用命令:');
                console.log('  start - 遠端啟動充電');
                console.log('  stop [transactionId] - 遠端停止充電');
                console.log('  reset - 重啟充電樁');
                console.log('  config - 讀取配置');
                console.log('  meter - 請求電錶數據');
                console.log('  help - 顯示幫助');
                break;
            default:
                if (command) {
                    console.log('未知命令，輸入 help 查看可用命令');
                }
        }
    }
});

// 啟動伺服器
const PORT = 8080;
server.listen(PORT, '0.0.0.0', () => {
    log(`🚀 增強版 OCPP 伺服器已啟動`, 'success');
    log(`   監聽位址: ws://0.0.0.0:${PORT}`, 'info');
    log(`   本機位址: ws://localhost:${PORT}`, 'info');
    log(`   區域網路: ws://*************:${PORT}`, 'info');
    log(``, 'info');
    log(`💡 支援功能:`, 'info');
    log(`   • 基本 OCPP 1.6 訊息處理`, 'info');
    log(`   • 遠端控制 (啟動/停止充電、重啟)`, 'info');
    log(`   • 電錶數據收集和處理`, 'info');
    log(`   • 即時狀態監控`, 'info');
    log(``, 'info');
    log(`🎮 命令行控制:`, 'info');
    log(`   輸入 help 查看可用命令`, 'info');
    log(``, 'info');
});

// 優雅關閉
process.on('SIGINT', () => {
    log('\n🛑 正在關閉伺服器...', 'info');
    wss.clients.forEach((ws) => {
        ws.close();
    });
    server.close(() => {
        log('✅ 伺服器已關閉', 'success');
        process.exit(0);
    });
});

process.on('uncaughtException', (error) => {
    log(`未捕捉的異常: ${error.message}`, 'error');
});

process.on('unhandledRejection', (reason, promise) => {
    log(`未處理的Promise拒絕: ${reason}`, 'error');
}); 