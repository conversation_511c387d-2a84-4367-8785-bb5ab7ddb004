const WebSocket = require('ws');
const http = require('http');
const url = require('url');

console.log('🔬 OCPP 診斷伺服器');
console.log('==================================================');
console.log('📋 此工具專門用於診斷充電樁連接問題');
console.log('🎯 重點解決：連接後立即斷開的問題');
console.log('');

class OCPPDiagnosticServer {
    constructor() {
        this.connectedChargers = new Map();
        this.messageCounter = 1;
        this.diagnosticData = [];
        this.server = null;
        this.wss = null;
    }

    start(port = 8080) {
        // 建立HTTP伺服器
        this.server = http.createServer();
        
        // 建立WebSocket伺服器，完全符合OCPP 1.6標準
        this.wss = new WebSocket.Server({
            server: this.server,
            handleProtocols: (protocols, request) => {
                console.log(`\n🔍 協定協商:`);
                console.log(`   客戶端請求協定: ${protocols}`);
                
                // 嚴格按照OCPP 1.6標準處理協定
                if (protocols.includes('ocpp1.6')) {
                    console.log(`   ✅ 選擇協定: ocpp1.6`);
                    return 'ocpp1.6';
                } else if (protocols.includes('ocpp2.0')) {
                    console.log(`   ✅ 選擇協定: ocpp2.0`);
                    return 'ocpp2.0';
                } else {
                    console.log(`   ❌ 不支援的協定，拒絕連接`);
                    return false;
                }
            },
            verifyClient: (info) => {
                const url = info.req.url;
                const origin = info.origin;
                const protocols = info.req.headers['sec-websocket-protocol'];
                
                console.log(`\n🔍 連接驗證:`);
                console.log(`   URL: ${url}`);
                console.log(`   Origin: ${origin || '未設定'}`);
                console.log(`   協定: ${protocols || '未設定'}`);
                console.log(`   IP: ${info.req.connection.remoteAddress}`);
                
                // 檢查URL格式
                if (!url || url === '/') {
                    console.log(`   ❌ 拒絕：缺少充電樁ID`);
                    return false;
                }
                
                // 檢查協定
                if (!protocols || (!protocols.includes('ocpp1.6') && !protocols.includes('ocpp2.0'))) {
                    console.log(`   ❌ 拒絕：缺少OCPP協定`);
                    return false;
                }
                
                console.log(`   ✅ 驗證通過`);
                return true;
            }
        });

        this.wss.on('connection', (ws, request) => {
            this.handleConnection(ws, request);
        });

        this.server.listen(port, '0.0.0.0', () => {
            console.log(`✅ OCPP診斷伺服器已啟動`);
            console.log(`   監聽端口: ${port}`);
            console.log(`   本機地址: ws://localhost:${port}`);
            console.log(`   區域網路: ws://*************:${port}`);
            console.log('');
            console.log('📝 使用方法:');
            console.log('1. 將充電樁Server URL設為: ws://*************:8080/20241024NJ1');
            console.log('2. 保存設定並重啟充電樁');
            console.log('3. 觀察此終端的詳細診斷輸出');
            console.log('');
            console.log('⏳ 等待充電樁連接...');
        });
    }

    handleConnection(ws, request) {
        const urlPath = request.url;
        const chargePointId = urlPath.substring(1); // 移除開頭的 '/'
        const clientIP = request.connection.remoteAddress;
        const protocol = ws.protocol;
        
        console.log(`\n🎉 新連接建立:`);
        console.log(`   充電樁ID: ${chargePointId}`);
        console.log(`   客戶端IP: ${clientIP}`);
        console.log(`   使用協定: ${protocol}`);
        console.log(`   連接時間: ${new Date().toLocaleString()}`);

        // 記錄診斷資料
        const connectionData = {
            timestamp: new Date(),
            chargePointId: chargePointId,
            clientIP: clientIP,
            protocol: protocol,
            status: 'connected'
        };
        this.diagnosticData.push(connectionData);

        // 儲存連接資訊
        this.connectedChargers.set(chargePointId, {
            ws: ws,
            protocol: protocol,
            clientIP: clientIP,
            connectedAt: new Date(),
            lastMessage: new Date(),
            messageCount: 0,
            bootNotificationReceived: false,
            heartbeatCount: 0
        });

        // 設定訊息處理
        ws.on('message', (data) => {
            this.handleMessage(ws, chargePointId, data);
        });

        // 設定連接關閉處理
        ws.on('close', (code, reason) => {
            console.log(`\n❌ 連接中斷:`);
            console.log(`   充電樁ID: ${chargePointId}`);
            console.log(`   關閉代碼: ${code}`);
            console.log(`   關閉原因: ${reason || '未指定'}`);
            console.log(`   連接持續時間: ${this.getConnectionDuration(chargePointId)}`);
            
            const charger = this.connectedChargers.get(chargePointId);
            if (charger) {
                console.log(`   訊息總數: ${charger.messageCount}`);
                console.log(`   BootNotification: ${charger.bootNotificationReceived ? '已收到' : '未收到'}`);
                console.log(`   心跳次數: ${charger.heartbeatCount}`);
            }
            
            this.connectedChargers.delete(chargePointId);
            this.analyzeProblem(chargePointId, code, reason);
        });

        // 設定錯誤處理
        ws.on('error', (error) => {
            console.log(`\n🚨 WebSocket錯誤:`);
            console.log(`   充電樁ID: ${chargePointId}`);
            console.log(`   錯誤訊息: ${error.message}`);
            console.log(`   錯誤代碼: ${error.code || '未知'}`);
        });

        console.log(`\n⏳ 等待 ${chargePointId} 發送BootNotification...`);
    }

    handleMessage(ws, chargePointId, data) {
        const charger = this.connectedChargers.get(chargePointId);
        if (charger) {
            charger.lastMessage = new Date();
            charger.messageCount++;
        }

        try {
            const message = JSON.parse(data.toString());
            const [messageType, messageId, action, payload] = message;

            console.log(`\n📨 收到訊息:`);
            console.log(`   來源: ${chargePointId}`);
            console.log(`   類型: ${messageType} (${this.getMessageTypeName(messageType)})`);
            console.log(`   ID: ${messageId}`);
            console.log(`   動作: ${action || '無'}`);
            console.log(`   內容: ${JSON.stringify(payload || {}, null, 2)}`);

            if (messageType === 2) { // CALL
                this.handleCall(ws, chargePointId, messageId, action, payload);
            } else if (messageType === 3) { // CALLRESULT
                this.handleCallResult(ws, chargePointId, messageId, payload);
            } else if (messageType === 4) { // CALLERROR
                this.handleCallError(ws, chargePointId, messageId, payload);
            }

        } catch (error) {
            console.log(`\n❌ 訊息解析錯誤:`);
            console.log(`   充電樁: ${chargePointId}`);
            console.log(`   原始資料: ${data.toString()}`);
            console.log(`   錯誤: ${error.message}`);
            
            // 發送格式錯誤回應
            this.sendCallError(ws, 'UNKNOWN', 'FormatViolation', '訊息格式錯誤', {});
        }
    }

    handleCall(ws, chargePointId, messageId, action, payload) {
        console.log(`\n🔄 處理 ${action} 請求...`);

        const charger = this.connectedChargers.get(chargePointId);

        switch (action) {
            case 'BootNotification':
                console.log(`\n🥾 BootNotification 詳細處理:`);
                console.log(`   供應商: ${payload.chargePointVendor || '未提供'}`);
                console.log(`   型號: ${payload.chargePointModel || '未提供'}`);
                console.log(`   序號: ${payload.chargePointSerialNumber || '未提供'}`);
                console.log(`   韌體版本: ${payload.firmwareVersion || '未提供'}`);

                // 標準BootNotification回應
                const bootResponse = {
                    currentTime: new Date().toISOString(),
                    interval: 300, // 5分鐘心跳間隔
                    status: 'Accepted'
                };

                this.sendCallResult(ws, messageId, bootResponse);
                
                if (charger) {
                    charger.bootNotificationReceived = true;
                }

                console.log(`   ✅ BootNotification已接受`);
                console.log(`   心跳間隔: ${bootResponse.interval}秒`);
                break;

            case 'Heartbeat':
                const heartbeatResponse = {
                    currentTime: new Date().toISOString()
                };

                this.sendCallResult(ws, messageId, heartbeatResponse);
                
                if (charger) {
                    charger.heartbeatCount++;
                }

                console.log(`   ✅ 心跳回應 (第${charger ? charger.heartbeatCount : '?'}次)`);
                break;

            case 'StatusNotification':
                console.log(`\n📊 StatusNotification 詳細:`);
                console.log(`   連接器ID: ${payload.connectorId}`);
                console.log(`   狀態: ${payload.status}`);
                console.log(`   錯誤代碼: ${payload.errorCode || 'NoError'}`);
                console.log(`   時間戳: ${payload.timestamp || '未提供'}`);

                this.sendCallResult(ws, messageId, {});
                console.log(`   ✅ StatusNotification回應已發送`);
                break;

            case 'MeterValues':
                console.log(`\n📊 MeterValues 詳細:`);
                console.log(`   連接器ID: ${payload.connectorId}`);
                if (payload.meterValue && payload.meterValue.length > 0) {
                    payload.meterValue.forEach((mv, index) => {
                        console.log(`   電錶值 ${index + 1}:`);
                        console.log(`     時間: ${mv.timestamp}`);
                        if (mv.sampledValue) {
                            mv.sampledValue.forEach((sv, svIndex) => {
                                console.log(`     數值 ${svIndex + 1}: ${sv.value} ${sv.unit || ''} (${sv.measurand || 'Unknown'})`);
                            });
                        }
                    });
                }

                this.sendCallResult(ws, messageId, {});
                console.log(`   ✅ MeterValues回應已發送`);
                break;

            default:
                console.log(`   ⚠️ 未處理的動作: ${action}`);
                this.sendCallError(ws, messageId, 'NotImplemented', `動作 ${action} 尚未實作`, {});
        }
    }

    handleCallResult(ws, chargePointId, messageId, payload) {
        console.log(`\n✅ 收到CallResult回應:`);
        console.log(`   充電樁: ${chargePointId}`);
        console.log(`   訊息ID: ${messageId}`);
        console.log(`   內容: ${JSON.stringify(payload, null, 2)}`);
    }

    handleCallError(ws, chargePointId, messageId, payload) {
        console.log(`\n❌ 收到CallError:`);
        console.log(`   充電樁: ${chargePointId}`);
        console.log(`   訊息ID: ${messageId}`);
        console.log(`   錯誤: ${JSON.stringify(payload, null, 2)}`);
    }

    sendCallResult(ws, messageId, payload) {
        const message = [3, messageId, payload];
        const messageString = JSON.stringify(message);
        
        console.log(`\n📤 發送CallResult:`);
        console.log(`   內容: ${messageString}`);
        
        ws.send(messageString);
    }

    sendCallError(ws, messageId, errorCode, errorDescription, errorDetails) {
        const message = [4, messageId, errorCode, errorDescription, errorDetails];
        const messageString = JSON.stringify(message);
        
        console.log(`\n📤 發送CallError:`);
        console.log(`   內容: ${messageString}`);
        
        ws.send(messageString);
    }

    getMessageTypeName(type) {
        switch (type) {
            case 2: return 'CALL';
            case 3: return 'CALLRESULT';
            case 4: return 'CALLERROR';
            default: return 'UNKNOWN';
        }
    }

    getConnectionDuration(chargePointId) {
        const charger = this.connectedChargers.get(chargePointId);
        if (!charger) return '未知';
        
        const duration = Date.now() - charger.connectedAt.getTime();
        const seconds = Math.floor(duration / 1000);
        const minutes = Math.floor(seconds / 60);
        
        if (minutes > 0) {
            return `${minutes}分${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }

    analyzeProblem(chargePointId, code, reason) {
        console.log(`\n🔬 問題分析:`);
        
        const charger = this.connectedChargers.get(chargePointId);
        
        if (code === 1006) {
            console.log(`   ❌ 異常關閉 (代碼1006)`);
            console.log(`   💡 可能原因:`);
            console.log(`      • 充電樁主動斷開連接`);
            console.log(`      • 網路連接不穩定`);
            console.log(`      • 充電樁不滿意伺服器回應`);
        } else if (code === 1002) {
            console.log(`   ❌ 協定錯誤 (代碼1002)`);
            console.log(`   💡 可能原因:`);
            console.log(`      • OCPP協定版本不匹配`);
            console.log(`      • 訊息格式錯誤`);
        } else if (code === 1000) {
            console.log(`   ✅ 正常關閉 (代碼1000)`);
        } else {
            console.log(`   ❓ 其他原因 (代碼${code})`);
        }

        if (!charger || !charger.bootNotificationReceived) {
            console.log(`   ⚠️ 未收到BootNotification - 這是主要問題！`);
            console.log(`   💡 建議解決方案:`);
            console.log(`      1. 檢查充電樁OCPP協定版本設定`);
            console.log(`      2. 確認充電樁ID設定正確`);
            console.log(`      3. 檢查網路連接穩定性`);
            console.log(`      4. 嘗試重啟充電樁`);
        }

        console.log(`\n📊 連接統計:`);
        console.log(`   總連接時間: ${this.getConnectionDuration(chargePointId)}`);
        console.log(`   訊息總數: ${charger ? charger.messageCount : 0}`);
        console.log(`   心跳次數: ${charger ? charger.heartbeatCount : 0}`);
    }

    showStatus() {
        console.log(`\n📊 診斷伺服器狀態:`);
        console.log(`   已連接充電樁: ${this.connectedChargers.size}`);
        
        if (this.connectedChargers.size > 0) {
            this.connectedChargers.forEach((charger, id) => {
                console.log(`   • ${id}:`);
                console.log(`     IP: ${charger.clientIP}`);
                console.log(`     協定: ${charger.protocol}`);
                console.log(`     連接時間: ${this.getConnectionDuration(id)}`);
                console.log(`     訊息數: ${charger.messageCount}`);
                console.log(`     心跳數: ${charger.heartbeatCount}`);
            });
        }
    }
}

// 啟動診斷伺服器
const diagnosticServer = new OCPPDiagnosticServer();
diagnosticServer.start(8080);

// 每30秒顯示狀態
setInterval(() => {
    diagnosticServer.showStatus();
}, 30000);

// 優雅關閉
process.on('SIGINT', () => {
    console.log('\n\n🛑 正在關閉診斷伺服器...');
    process.exit(0);
}); 